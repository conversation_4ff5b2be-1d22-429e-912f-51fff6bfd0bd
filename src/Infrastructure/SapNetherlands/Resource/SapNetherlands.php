<?php

declare(strict_types=1);

namespace App\Infrastructure\SapNetherlands\Resource;

use App\Infrastructure\SapGermany\Dto\Input\TourDto;
use App\Infrastructure\SapGermany\Dto\Output\TourIngested;
use App\Infrastructure\SapNetherlands\HttpEndpoint\TourEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;

#[ApiResource(
    area: 'sap_netherlands',
    operations: [
        new Get(
            controller: TourEndpoint::class,
            controllerAction: 'ping',
            uriTemplate: '/ping',
            responseDescription: 'Ping successful',
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
        new Post(
            controller: TourEndpoint::class,
            controllerAction: 'tour',
            uriTemplate: '/tour',
            input: TourDto::class,
            responseDescription: 'Tour successful imported',
            output: TourIngested::class,
            successHttpCode: 200,
        ),
        new Delete(
            controller: TourEndpoint::class,
            controllerAction: 'invalidateTour',
            uriTemplate: '/tour/{tourExternalId}',
            urlRequirements: [
                'tourExternalId' => '.{1,100}',
            ],
            pathParameters: [
                new PathParameter(
                    name: 'tourExternalId',
                    type: 'string',
                    description: 'External ID of the tour to invalidate',
                ),
            ],
            responseDescription: 'Tour successfully invalidated',
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'SAP Netherlands',
)]
class SapNetherlands
{
}
