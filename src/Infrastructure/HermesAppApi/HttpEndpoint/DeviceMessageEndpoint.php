<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\HttpEndpoint;

use App\Domain\Application\DeviceMessageService;
use App\Domain\MessageQueue\MessageDispatcherInterface;
use App\Domain\Repository\DeviceMessageThreadRepository;
use App\Domain\Security\Security;
use App\Domain\Services\ObjectStorage\ObjectPrefix;
use App\Domain\Services\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Services\ObjectStorage\ObjectStorageException;
use App\Domain\Services\SessionService;
use App\Infrastructure\HermesAppApi\MessageQueue\DeviceMessage\DeviceMessageMarkAsReadMessage;
use App\Infrastructure\HermesAppApi\Resource\DeviceMessage;
use App\Infrastructure\HermesAppApi\Resource\DeviceMessageThread;
use App\Infrastructure\HermesAppApi\Resource\Dto\DeviceMessageMarkAsRead;
use PreZero\ApiBundle\Collection\Collection;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;

#[AsController]
readonly class DeviceMessageEndpoint
{
    public function __construct(
        private Security $security,
        private ObjectRepositoryInterface $objectRepository,
        private LoggerInterface $logger,
        private DeviceMessageService $deviceMessageService,
        private DeviceMessageThreadRepository $deviceMessageThreadRepository,
        private SessionService $sessionService,
        private MessageDispatcherInterface $messageDispatcher,
    ) {
    }

    /**
     * @return Collection<DeviceMessageThread>
     */
    public function getList(): Collection
    {
        $session = $this->sessionService->getActiveDeviceSession();
        $staffs = [];
        $equipments = [];

        foreach ($session->getActiveSessionUsers() as $sessionUser) {
            $staffs[] = $sessionUser->getStaff();
        }

        foreach ($session->getActiveSessionEquipments() as $sessionEquipment) {
            $equipments[] = $sessionEquipment->getEquipment();
        }

        $threads = $this->deviceMessageThreadRepository->findByStaffAndEquipments($staffs, $equipments);

        return new Collection(
            items: array_map(DeviceMessageThread::fromEntity(...), $threads),
        );
    }

    public function createNew(DeviceMessage $deviceMessage): DeviceMessage
    {
        $user = $this->security->getAuthenticatedUser();
        $staff = $user->getStaff();
        assert(null !== $staff);
        $this->validateAttachments($deviceMessage->attachments);

        if (null === $deviceMessage->threadId) {
            $thread = $this->deviceMessageThreadRepository->findThreadByRecipient($staff);

            if (null === $thread) {
                return DeviceMessage::fromEntity($this->deviceMessageService->startThreadFromDevice(
                    sender: $user,
                    text: $deviceMessage->message,
                    attachments: $deviceMessage->attachments,
                ));
            }

            return DeviceMessage::fromEntity($thread->replyFromDevice(
                deviceUser: $user,
                text: $deviceMessage->message,
                attachments: $deviceMessage->attachments,
            ));
        }

        $thread = $this->deviceMessageThreadRepository->find($deviceMessage->threadId);

        if (null === $thread) {
            $this->logger->critical(
                'Cannot send message to non-existing thread',
                ['thread_id' => $deviceMessage->threadId],
            );

            throw new BadRequestHttpException('Thread not found');
        }

        return DeviceMessage::fromEntity($thread->replyFromDevice(
            deviceUser: $user,
            text: $deviceMessage->message,
            attachments: $deviceMessage->attachments,
        ));
    }

    public function markAsRead(DeviceMessageMarkAsRead $dto): void
    {
        $this->messageDispatcher->dispatch(new DeviceMessageMarkAsReadMessage(messageIds: $dto->messageIds));
    }

    /**
     * @param array<string> $attachments
     */
    private function validateAttachments(array $attachments): void
    {
        foreach ($attachments as $attachment) {
            if (!str_contains($attachment, ObjectPrefix::DEVICE_MESSAGE->value)) {
                throw new BadRequestHttpException('Invalid attachment, expected device-message prefix');
            }

            try {
                if (!$this->objectRepository->exists($attachment)) {
                    throw new BadRequestHttpException('Attachment not found');
                }
            } catch (ObjectStorageException $e) {
                throw new ServiceUnavailableHttpException(previous: $e);
            }
        }
    }
}
