<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\HttpEndpoint;

use App\Domain\Context\TenantContext;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class DataProtectionNoticeEndpoint
{
    public function __construct(
        #[Autowire('%kernel.project_dir%')]
        private string $projectPath,
        private TenantContext $tenantContext,
    ) {
    }

    public function get(string $locale): Response
    {
        $dataProtectionNoticePath = sprintf(
            '%s/assets/data-protection-notice/tenant-%s-locale-%s.md',
            $this->projectPath,
            $this->tenantContext->getTenant()->value,
            $locale,
        );

        if (!file_exists($dataProtectionNoticePath)) {
            throw new NotFoundHttpException();
        }

        $noticeContent = file_get_contents($dataProtectionNoticePath);

        if (false === $noticeContent) {
            throw new \RuntimeException('Failed to read data protection notice file');
        }

        return new Response(
            content: $noticeContent,
            headers: [
                'Content-Type' => 'text/markdown',
            ]
        );
    }
}
