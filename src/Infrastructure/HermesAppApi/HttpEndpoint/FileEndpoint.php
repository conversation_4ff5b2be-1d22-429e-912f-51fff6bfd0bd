<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\HttpEndpoint;

use App\Domain\Context\TenantContext;
use App\Domain\Security\Security;
use App\Domain\Services\ObjectStorage\FileObject;
use App\Domain\Services\ObjectStorage\ObjectMetadata;
use App\Domain\Services\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Services\ObjectStorage\ObjectStorageException;
use App\Infrastructure\HermesAppApi\Exception\ApiFileException;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;
use Symfony\Component\Mime\MimeTypes;

#[AsController]
readonly class FileEndpoint
{
    public function __construct(
        private ObjectRepositoryInterface $objectRepository,
        private LoggerInterface $logger,
        private Security $security,
        private TenantContext $tenantContext,
    ) {
    }

    public function store(string $directory, string $fileName, Request $request): void
    {
        $userIdentifier = $this->security->getAuthenticatedUser()->getUserIdentifier();

        try {
            if ($this->objectRepository->exists($directory.'/'.$fileName)) {
                throw ApiFileException::createException(
                    ApiFileException::FILE_ID_CONFLICT,
                    'file id conflict',
                    'file id already exists',
                    code: Response::HTTP_CONFLICT
                );
            }
        } catch (ObjectStorageException $e) {
            throw new ServiceUnavailableHttpException(previous: $e);
        }

        $fileContents = $this->getFileContent($request->getContent());

        $tempFileName = tempnam(sys_get_temp_dir(), 'upload-temp-');
        file_put_contents($tempFileName, $fileContents);

        $mimeTypes = new MimeTypes();
        $mimeType = $mimeTypes->guessMimeType($tempFileName);

        $fileObject = new FileObject(
            identifier: $directory.'/'.$fileName,
            content: $fileContents,
            mimeType: $mimeType ?? 'application/octet-stream',
            objectMetadata: new ObjectMetadata(
                tenantIdentifier: $this->tenantContext->getTenant()->value,
                userIdentifier: $userIdentifier,
            ),
        );

        try {
            $this->objectRepository->store($fileObject);
        } catch (ObjectStorageException $e) {
            $this->logger->critical(
                'Could not store a file in object storage',
                ['exception' => $e]
            );

            throw new ServiceUnavailableHttpException('Could not store a file in object storage');
        } finally {
            unlink($tempFileName);
        }
    }

    public function retrieve(string $directory, string $fileName): Response
    {
        try {
            $fileObject = $this->objectRepository->get($directory.'/'.$fileName);
        } catch (ObjectStorageException $e) {
            $this->logger->critical(
                'Could not download a file from object storage',
                ['exception' => $e]
            );

            throw new ServiceUnavailableHttpException();
        }

        if (!$fileObject instanceof FileObject) {
            throw ApiFileException::createException(type: ApiFileException::FILE_NOT_FOUND, title: 'file not found', detail: 'file not found', code: Response::HTTP_NOT_FOUND);
        }

        if (!$this->security->canAccessFileObject($fileObject)) {
            throw ApiFileException::createException(ApiFileException::FILE_NO_ACCESS, 'Access Denied', 'You don\'t have permission to access this file');
        }

        return new Response(
            content: $fileObject->content,
            status: 200,
            headers: [
                'Content-Type' => $fileObject->mimeType,
            ],
        );
    }

    private function getFileContent(string $fileContent): string
    {
        $base64decoded = base64_decode($fileContent, true);

        if (
            false !== $base64decoded
            && base64_encode($base64decoded) === $fileContent
        ) {
            return $base64decoded;
        }

        return $fileContent;
    }
}
