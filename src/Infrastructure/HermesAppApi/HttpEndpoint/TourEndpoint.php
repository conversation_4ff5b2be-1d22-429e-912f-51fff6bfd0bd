<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\HttpEndpoint;

use App\Domain\Context\DeviceContext;
use App\Domain\Entity\AccessibleTermination;
use App\Domain\Entity\AccessibleTerminationRelation;
use App\Domain\Entity\Enum\AppMessages\BookingMessage;
use App\Domain\Entity\Session;
use App\Domain\Entity\Termination as TerminationEntity;
use App\Domain\Entity\Tour as TourEntity;
use App\Domain\Exception\WorkflowTransitionException;
use App\Domain\MessageQueue\MessageDispatcherInterface;
use App\Domain\Repository\TourRepository;
use App\Domain\Security\Security;
use App\Domain\Services\SessionService;
use App\Domain\Workflow\GuardContext;
use App\Domain\Workflow\Workflow;
use App\Infrastructure\HermesAppApi\DtoBuilder\TourDtoBuilder;
use App\Infrastructure\HermesAppApi\DtoBuilder\TourSummaryDtoBuilder;
use App\Infrastructure\HermesAppApi\Exception\AuthException;
use App\Infrastructure\HermesAppApi\MessageQueue\Interruption\InterruptionBookingMessage;
use App\Infrastructure\HermesAppApi\MessageQueue\Interruption\InterruptionChangeTaskGroupMessage;
use App\Infrastructure\HermesAppApi\MessageQueue\Tour\TourChangeTaskGroupMessage;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingResponse;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingResult;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingValue;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\InterruptionBookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\ResultReason;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroup;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroupTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Termination;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\InterruptionSource;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\TerminationHistory;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\TourSummary;
use App\Infrastructure\HermesAppApi\Resource\Tour;
use App\Infrastructure\HermesAppApi\Service\LocationFormatService;
use App\Infrastructure\HermesAppApi\Service\MapContext;
use App\Infrastructure\HermesAppApi\Service\TerminationService;
use App\Infrastructure\PortalApi\Exception\TranslatableHttpException;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class TourEndpoint
{
    public function __construct(
        private SessionService $sessionService,
        private DeviceContext $deviceContext,
        private TourRepository $tourRepository,
        private LocationFormatService $locationFormatService,
        private Workflow $workflow,
        private Security $security,
        private TourDtoBuilder $tourDtoBuilder,
        private TourSummaryDtoBuilder $tourSummaryDtoBuilder,
        private GuardContext $guardContext,
        private MessageDispatcherInterface $messageDispatcher,
        private TerminationService $terminationService,
    ) {
    }

    /**
     * @return Collection<TerminationHistory>
     */
    public function getTerminations(string $tourId): Collection
    {
        $activeSession = $this->sessionService->getActiveDeviceSessionByDeviceId($this->deviceContext->getDeviceId());

        if (null === $activeSession) {
            throw AuthException::sessionInvalid();
        }

        $tour = $this->tourRepository->find($tourId);

        if (null === $tour) {
            throw new NotFoundHttpException('Tour not found');
        }

        $mapContext = new MapContext()->setLocationFormatService($this->locationFormatService);

        return new Collection(
            items: array_map(
                static fn (TerminationEntity $termination): TerminationHistory
                    => TerminationHistory::fromTerminationEntity($termination, $mapContext),
                $tour->getTerminations()->toArray(),
            ),
        );
    }

    public function terminateTour(string $tourId, Termination $termination): BookingResponse
    {
        $session = $this->sessionService->getActiveDeviceSessionByDeviceId($this->deviceContext->getDeviceId());

        if (!$session instanceof Session) {
            throw new NotFoundHttpException('Session not found');
        }

        $tour = $this->tourRepository->find($tourId);

        if (null === $tour) {
            throw new NotFoundHttpException('Tour not found');
        }

        /** @var AccessibleTermination|false $terminationTemplate */
        $terminationTemplate = $tour->getAccessibleTerminationRelations()
            ->map(fn (AccessibleTerminationRelation $atr): AccessibleTermination => $atr->getAccessibleTermination())
            ->filter(fn (AccessibleTermination $t): bool => $t->getId() === $termination->templateUuid)
            ->first();

        if (!$terminationTemplate) {
            throw new NotFoundHttpException('Termination template not found');
        }

        $terminationEntity = $terminationTemplate->createTermination();
        $tour->addTermination($terminationEntity);

        foreach ($termination->taskGroups as $taskGroup) {
            $this->terminationService->addTaskGroupToTermination($terminationEntity, $terminationTemplate, $taskGroup);
        }

        try {
            $this->workflow->applyForTour(
                tour: $tour,
                transition: 'terminate',
            );
        } catch (WorkflowTransitionException $e) {
            $reasons = [];

            foreach ($e->getReasons() as $reason) {
                $reasons[] = new ResultReason(
                    key: $reason,
                    title: BookingMessage::getTitleTranslationTag($reason),
                    description: BookingMessage::getMessageTranslationTag($reason),
                );
            }

            return new BookingResponse(
                uuid: $tour->getId(),
                booking: BookingResult::UNPROCESSED,
                reasons: $reasons,
            );
        }

        return new BookingResponse(
            uuid: $tourId,
            booking: BookingResult::PROCESSED,
        );
    }

    /**
     * @return Collection<TourSummary>
     */
    public function getList(): Collection
    {
        $activeSession = $this->sessionService->getActiveDeviceSessionByDeviceId($this->deviceContext->getDeviceId());

        if (null === $activeSession) {
            throw AuthException::sessionInvalid();
        }

        $availableTours = $this->tourRepository->getAvailableToursForSession($activeSession);
        $availableTours = array_values(array_filter(
            $availableTours,
            fn (TourEntity $tour): bool => $this->security->isGranted('view', $tour)
        ));

        return new Collection(
            items: array_map(
                fn (TourEntity $tour): TourSummary => $this->tourSummaryDtoBuilder->buildFromTourEntity($tour),
                $availableTours
            ),
        );
    }

    public function getById(string $uuid, Request $request): Tour
    {
        $includeAll = (1 === (int) $request->query->get('includeAll'));

        $tour = $this->tourRepository->find($uuid);

        if (null === $tour) {
            throw new NotFoundHttpException('Tour not found');
        }

        if (!$this->security->isGranted('view', $tour)) {
            throw new TranslatableHttpException(Response::HTTP_FORBIDDEN, 'You are not allowed to view this tour.', 'tour/error/access-denied');
        }

        return $this->tourDtoBuilder->buildFromTourEntity($tour, $includeAll);
    }

    public function booking(string $tourId, BookingRequest $bookingRequest): BookingResponse
    {
        $session = $this->sessionService->getActiveDeviceSessionByDeviceId($this->deviceContext->getDeviceId());

        if (!$session instanceof Session) {
            throw new NotFoundHttpException('Session not found');
        }

        $tour = $this->tourRepository->find($tourId);

        if (null === $tour) {
            throw new NotFoundHttpException('Tour not found');
        }

        if (!$this->security->isGranted('view', $tour)) {
            throw new TranslatableHttpException(Response::HTTP_FORBIDDEN, 'You are not allowed to book this tour.', 'tour/error/access-denied');
        }

        $this->guardContext->setForceTourTransition($bookingRequest->force);

        if (BookingValue::START !== $bookingRequest->booking && BookingValue::END !== $bookingRequest->booking) {
            throw new BadRequestHttpException('Only booking values "start" and "end" are allowed');
        }

        try {
            $this->workflow->applyForTour(
                tour: $tour,
                transition: $bookingRequest->booking->value,
            );
        } catch (WorkflowTransitionException $e) {
            $reasons = [];

            foreach ($e->getReasons() as $reason) {
                $reasons[] = new ResultReason(
                    key: $reason,
                    title: BookingMessage::getTitleTranslationTag($reason),
                    description: BookingMessage::getMessageTranslationTag($reason),
                );
            }

            return new BookingResponse(
                uuid: $tour->getId(),
                booking: BookingResult::UNPROCESSED,
                reasons: $reasons,
            );
        }

        return new BookingResponse(
            uuid: $tour->getId(),
            booking: BookingResult::PROCESSED,
        );
    }

    public function completeTaskgroup(string $tourId, TaskGroup $taskGroup): void
    {
        $this->messageDispatcher->dispatch(new TourChangeTaskGroupMessage(
            tourId: $tourId,
            taskGroup: $taskGroup
        ));
    }

    public function interruptionTaskgroup(TaskGroupTemplate $taskGroupTemplate, string $clientUuid): void
    {
        $this->messageDispatcher->dispatch(new InterruptionChangeTaskGroupMessage(
            clientId: $clientUuid,
            taskGroup: $taskGroupTemplate,
        ));
    }

    public function interruptionBooking(InterruptionBookingRequest $bookingRequest, string $uuid): void
    {
        $this->messageDispatcher->dispatch(new InterruptionBookingMessage(
            interruptionId: $uuid,
            source: InterruptionSource::TOUR,
            bookingRequest: $bookingRequest,
        ));
    }
}
