<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Documentation;

readonly class EquipmentEndpointDocumentation
{
    public const string GET_LIST_SUMMARY = 'get list of equipments (sync, directly forwarded to backend).disposed for session';
    public const string GET_LIST_DESCRIPTION = <<<'ENDPNDDESCRIPTION'
        currently equipments are shown that are related to one of the tours that would appear in the call of related tours (/api/v2/tour) as a dispatched equipment or connected by tour-start and session.
        (When a tour is started and equipment is in the session, that is not dispatched in the tour, this equipment becomes set related to the tour) If
        no such equipments exist, all equipments of session-related branches (by the branches, that are related to the staff in the session) are shown. if availability=all is submitted as query-param, also all
        equipments of session-related branches (by staff) are shown.The status is set relatively to the current own session - inside the own session with end===null means equipped, otherwise unequipped
        ENDPNDDESCRIPTION;

    public const string GET_SEARCH_SUMMARY = 'get list of accessible equipments (sync, directly forwarded to backend).';
    public const string GET_SEARCH_DESCRIPTION = <<<'ENDPNDDESCRIPTION'
        Get a list of available equipments of certain types or type-categories
        ENDPNDDESCRIPTION;

    public const string GET_ITEM_SUMMARY = 'details of a single equipment. (sync, directly forwarded to backend).';
    public const string GET_ITEM_DESCRIPTION = <<<'ENDPNDDESCRIPTION'
        status results from being represented as active in the own session. If the equipment is connected to a session of the own device and the end-timestamp of the session is not set,
        the equipment is in status "equipped", else it is "unequipped".

        in taskgroups, taskgroups are shown, that have been created implicitly on booking the equipment with start.
        those taskgroup must be patched, before an equipment can be successfully booked with "end"
        ENDPNDDESCRIPTION;

    public const string BOOKING_SUMMARY = 'set equipment in the session / set it ended. (directly sync).';
    public const string BOOKING_DESCRIPTION = <<<'ENDPNDDESCRIPTION'
        - booking "start" creates an entry in sessionEquipment if equipment is not in session with end===null yet.
        - the equipment's status is changed between available and in_use.
        - a new start creates config-related taskgroups related to the sessionEquipment, that have to be completed, before a booking "end" for the
        equipment is possible.
        - End demands those taskgroups being completed, otherwise the response is "unprocessed" (processed in success-case)
        - if equipment is booked out ("end") implicitly by logging out the last user of a session, possible open taskGroups of an equipment are ignored. This happens if the last remaining user of a session logs in on another device.
        Then the existing session is ended: The equipments are set as ended in the session and the become "available" again
        ENDPNDDESCRIPTION;

    public const string COMPLETE_TASKGROUP_SUMMARY = 'Completion of equipment-taskgroups. (async).';
    public const string COMPLETE_TASKGROUP_DESCRIPTION = <<<'ENDPNDDESCRIPTION'
        Taskgroups, that have been created on equipment-booking start and having to be completed before processed booking end of the equipment is possible
        ENDPNDDESCRIPTION;

    public const string INTERRUPTION_TASKGROUP_SUMMARY = 'Fill the taskgroups of a created equipment-interruption referenced with the app-side created uuid (async).';
    public const string INTERRUPTION_TASKGROUP_DESCRIPTION = <<<'ENDPNDDESCRIPTION'
        Filling the interruption's taskGroup-object using the tasks and inputs of the template. Not sent taskGroups or tasks in taskGroups will not be filled and related to the created interruption.
        The interruption itself must have been created before this call by usage of the POST:api/v2/equipment/interruption/{uuid}/booking-Endpoint.

        The field template_uuid in taskgroups, tasks and inputs MUST be the same as the template_uuid in the template to reference the content in the backend!

        Important: template_uuid-attributes are used for
        referencing the original fields in the template, not uuid-fields! Those are ignored when processing a PATCH on an interruption-taskgroup.
        Taskgroups of an interruption-template, that are not sent as PATCH-request before the interruption is booked as ended, are not stored in the backend.
        ENDPNDDESCRIPTION;

    public const string INTERRUPTION_BOOKING_SUMMARY = 'creates a new interruption/end an existing for equipment with interruption template {uuid} (async).';
    public const string INTERRUPTION_BOOKING_DESCRIPTION = <<<'ENDPNDDESCRIPTION'
        when an interruption is created, an app-created uuid "clientUuid" has to be sent and the same has to be used to end this specific interruption.
        The creation happens on booking:start.

        The interruption is created empty (without taskgroup). those will be enriched by following patches.
        An interruption has to be started with an uuid created on client, before a PATCH-call on taskGroups can be processed.
        ENDPNDDESCRIPTION;
}
