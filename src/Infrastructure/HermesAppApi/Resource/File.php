<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\FileEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Put(
            controller: [FileEndpoint::class, 'store'],
            summary: 'Upload a file to a directory.',
            uriTemplate: '/file/{directory}/{fileName}',
            pathParameters: [
                new PathParameter(
                    name: 'directory',
                    type: 'string',
                    description: 'The directory of the file: app-documents|app-user-files|device-message|vehicle-inspection',
                ),
                new PathParameter(
                    name: 'fileName',
                    type: 'string',
                    description: 'The name of the file (uuid)',
                ),
            ],
            requestType: ContentType::BINARY,
            requestDescription: 'Binary file contents',
            responseDescription: 'File uploaded',
            responseType: ContentType::EMPTY,
            successHttpCode: 201,
        ),
        new Get(
            controller: [FileEndpoint::class, 'retrieve'],
            summary: 'Download a file from a directory.',
            uriTemplate: '/file/{directory}/{fileName}', pathParameters: [
                new PathParameter(
                    name: 'directory',
                    type: 'string',
                    description: 'The directory of the file: app-documents|app-user-files|device-message|vehicle-inspection',
                ),
                new PathParameter(
                    name: 'fileName',
                    type: 'string',
                    description: 'The name of the file (uuid)',
                ),
            ],
            responseDescription: 'Binary file contents',
            responseType: ContentType::BINARY,
        ),
    ],
    identifier: null,
    tag: 'File',
)]
class File
{
}
