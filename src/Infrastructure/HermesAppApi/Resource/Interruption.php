<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\InterruptionEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\InterruptionHistory;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\InterruptionTemplate;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Enum\Pagination;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [InterruptionEndpoint::class, 'templates'],
            summary: 'Gives back the list of possible interruptions seperated by staff, tour and equipment.',
            uriTemplate: '/interruption-templates',
            pagination: Pagination::NONE,
            output: InterruptionTemplate::class,
        ),
        new GetCollection(
            controller: [InterruptionEndpoint::class, 'list'],
            summary: 'Gives a list of the created interruptions object of the current session.',
            uriTemplate: '/interruption',
            queryParameters: [
                new QueryParameter(
                    name: 'includeNotCompleted',
                    type: 'boolean',
                    description: 'Whether to include not completed interruptions in the response. If not set, only completed interruptions are returned.',
                ),
            ],
            pagination: Pagination::NONE,
            output: InterruptionHistory::class,
        ),
    ],
    identifier: null,
    tag: 'Interruption',
)]
class Interruption
{
}
