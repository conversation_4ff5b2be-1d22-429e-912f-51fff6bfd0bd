<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\Enum\DeviceMessageFrom;
use App\Infrastructure\HermesAppApi\Documentation\DeviceMessageDocumentation;
use App\Infrastructure\HermesAppApi\HttpEndpoint\DeviceMessageEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\DeviceMessageMarkAsRead;
use Dokky\Attribute\Property;
use Dokky\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Post(
            controller: [DeviceMessageEndpoint::class, 'createNew'],
            summary: DeviceMessageDocumentation::CREATE_NEW_MESSAGE_SUMMARY,
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
        ),
        new Post(
            controller: [DeviceMessageEndpoint::class, 'markAsRead'],
            summary: DeviceMessageDocumentation::MARK_AS_READ_SUMMARY,
            uriTemplate: '/device-message/mark-as-read',
            input: DeviceMessageMarkAsRead::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
    ],
    tag: 'Device Message',
)]
class DeviceMessage
{
    public const string GROUP_CREATE = 'create';
    public const string GROUP_READ = 'read';

    #[Groups([self::GROUP_READ])]
    public string $id;

    #[Groups([self::GROUP_READ])]
    public string $username;

    #[Groups([self::GROUP_READ])]
    public DeviceMessageFrom $from;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 500)]
    #[Groups([self::GROUP_READ, self::GROUP_CREATE])]
    public string $message;

    /**
     * @var array<string>
     */
    #[Property(
        schema: new Schema(
            examples: ['device-message/da8d1afe-01f1-4d25-b6e3-eedca0b92f06'],
        )
    )]
    #[Assert\All([
        new Assert\NotBlank(),
        new Assert\Length(max: 255),
    ])]
    #[Groups([self::GROUP_READ, self::GROUP_CREATE])]
    public array $attachments = [];

    #[SerializedName('thread_id')]
    #[Assert\Uuid]
    #[Groups([self::GROUP_CREATE])]
    public ?string $threadId = null;

    #[Groups([self::GROUP_READ])]
    #[SerializedName('created_at')]
    public \DateTimeImmutable $createdAt;

    #[Groups([self::GROUP_READ])]
    #[SerializedName('is_read')]
    public bool $isRead;

    public static function fromEntity(\App\Domain\Entity\DeviceMessage $entity): self
    {
        $resource = new self();
        $resource->id = $entity->getId();
        $resource->username = $entity->getSenderUsername();
        $resource->from = $entity->getMessageFrom();
        $resource->message = $entity->getText();
        $resource->attachments = $entity->getAttachments();
        $resource->threadId = $entity->getThread()->getId();
        $resource->createdAt = $entity->getCreatedAt();
        $resource->isRead = $entity->isFromDevice() || $entity->isRead();

        return $resource;
    }
}
