<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\EquipmentTypeEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\EquipmentTypeOptionList;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Get(
            controller: [EquipmentTypeEndpoint::class, 'getEquipmentTypes'],
            uriTemplate: '/equipment-types',
            responseDescription: 'List of Equipment types',
            output: EquipmentTypeOptionList::class,
            responseOpenApiSchemaName: 'EquipmentTypeListItem',
        ),
        new Get(
            controller: [EquipmentTypeEndpoint::class, 'getEquipmentTypeCategories'],
            uriTemplate: '/equipment-type-categories',
            responseDescription: 'List of Equipment type categories',
            output: EquipmentTypeOptionList::class,
            responseOpenApiSchemaName: 'EquipmentTypeCategoryListItem',
        ),
    ],
    identifier: null,
    tag: 'Equipment',
)]
class EquipmentType
{
}
