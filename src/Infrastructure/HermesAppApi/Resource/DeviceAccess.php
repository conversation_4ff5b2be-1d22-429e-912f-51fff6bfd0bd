<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\DeviceAccess as DeviceAccessEntity;
use App\Domain\Entity\Enum\DeviceAccessType;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\HermesAppApi\Documentation\DeviceAccessDocumentation;
use App\Infrastructure\HermesAppApi\HttpEndpoint\DeviceAccessEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [DeviceAccessEndpoint::class, 'getListOfActiveDeviceAccess'],
            summary: DeviceAccessDocumentation::GET_LIST_SUMMARY,
            pagination: Pagination::NONE,
        ),
    ],
    identifier: null,
    tag: 'Device Access',
    security: 'is_granted("'.UserRole::ROLE_DRIVER->value.'")',
)]
class DeviceAccess
{
    #[SerializedName('access_type')]
    public DeviceAccessType $accessType;

    #[SerializedName('access_expires')]
    public int $accessExpires;

    public static function fromEntity(DeviceAccessEntity $deviceAccessEntity): self
    {
        $resource = new self();
        $resource->accessType = $deviceAccessEntity->getDeviceAccessType();
        $resource->accessExpires = $deviceAccessEntity->getValidUntil()->getTimestamp() - time();

        return $resource;
    }
}
