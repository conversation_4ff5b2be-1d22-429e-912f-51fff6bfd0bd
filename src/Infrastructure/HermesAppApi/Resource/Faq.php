<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\FaqEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Faq\FaqQuestion;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [FaqEndpoint::class, 'getLanguages'],
            summary: 'Get list of FAQ information.',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [Faq::GROUP_LIST]],
        ),
        new Get(
            controller: [FaqEndpoint::class, 'getLanguage'],
            summary: 'Get list of FAQ for a specific language by ISO.',
        ),
    ],
    identifier: 'iso',
    tag: 'FAQ',
)]
class Faq
{
    public const string GROUP_LIST = 'list';

    #[Groups([self::GROUP_LIST])]
    public string $iso;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('last_update')]
    public ?\DateTimeImmutable $lastUpdate = null;

    /**
     * @var array<FaqQuestion>
     */
    public array $questions = [];

    /**
     * @param array<\App\Domain\Entity\Faq> $faqEntities
     */
    public static function fromEntityList(string $iso, array $faqEntities): self
    {
        $lastUpdated = null;
        $questions = [];

        foreach ($faqEntities as $faqEntity) {
            $questions[] = FaqQuestion::fromEntity($faqEntity);
            $entityLastModified = $faqEntity->getModifiedAt();

            if (null === $lastUpdated || $entityLastModified > $lastUpdated) {
                $lastUpdated = $entityLastModified;
            }
        }

        $resource = new self();
        $resource->iso = $iso;
        $resource->lastUpdate = $lastUpdated;
        $resource->questions = $questions;

        return $resource;
    }
}
