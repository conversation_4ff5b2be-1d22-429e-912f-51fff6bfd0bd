<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common;

use App\Domain\Entity\Enum\RuleOperator;
use App\Domain\Entity\Enum\Types\ElementType;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\Element\ElementInterface;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\OrderTaskGroup;

class RuleProcessor
{
    /**
     * @template T of TaskGroup|TaskGroupTemplate|OrderTaskGroup
     *
     * @param array<T> $taskGroups
     *
     * @phpstan-return array<T>
     */
    public static function processTaskGroupsRules(array $taskGroups): array
    {
        /** @var TaskGroup $taskGroup */
        foreach ($taskGroups as $taskGroup) {
            foreach ($taskGroup->rules as $rule) {
                if (RuleOperator::EMPTY === $rule->operator || RuleOperator::NOT_EMPTY === $rule->operator) {
                    continue;
                }

                $rule->value = match (self::getKeyElementTypeFromTaskGroup($taskGroups, $rule->elementId)) {
                    ElementType::NUMBER => self::isFloat($rule->value) ? (float) $rule->value : (int) $rule->value,
                    ElementType::ACCEPT, ElementType::BOOLEAN => null === $rule->value ? null : '1' === $rule->value,
                    default => $rule->value,
                };
            }

            self::processTaskRules($taskGroup->tasks, $taskGroups);
        }

        return $taskGroups;
    }

    /**
     * @param Task[]|TaskTemplate[]                            $tasks
     * @param TaskGroup[]|TaskGroupTemplate[]|OrderTaskGroup[] $searchTaskGroups
     */
    private static function processTaskRules(array $tasks, array $searchTaskGroups): void
    {
        foreach ($tasks as $task) {
            foreach ($task->rules as $index => $rule) {
                $keyElement = self::findKeyElement($searchTaskGroups, $rule->elementId);

                if (null === $keyElement) {
                    unset($task->rules[$index]);
                    continue;
                }

                if (RuleOperator::EMPTY === $rule->operator || RuleOperator::NOT_EMPTY === $rule->operator) {
                    continue;
                }

                $rule->value = match (ElementType::from($keyElement->getType())) {
                    ElementType::NUMBER => self::isFloat($rule->value) ? (float) $rule->value : (int) $rule->value,
                    ElementType::ACCEPT, ElementType::BOOLEAN => null === $rule->value ? null : '1' === $rule->value,
                    default => $rule->value,
                };
            }

            $task->rules = array_values($task->rules);
        }
    }

    private static function isFloat(mixed $value): bool
    {
        return is_float($value) || (is_string($value) && str_contains($value, '.'));
    }

    /**
     * @param TaskGroup[]|TaskGroupTemplate[]|OrderTaskGroup[] $taskGroups
     */
    private static function getKeyElementTypeFromTaskGroup(array $taskGroups, string $elementId): ?ElementType
    {
        $keyElement = self::findKeyElement($taskGroups, $elementId);

        if (null !== $keyElement) {
            return ElementType::from($keyElement->getType());
        }

        return null;
    }

    /**
     * @param TaskGroup[]|TaskGroupTemplate[]|OrderTaskGroup[] $taskGroups
     */
    private static function findKeyElement(array $taskGroups, string $elementId): ?ElementInterface
    {
        foreach ($taskGroups as $taskGroup) {
            foreach ($taskGroup->tasks as $task) {
                foreach ($task->inputs as $input) {
                    if ($input->getUuid() === $elementId) {
                        return $input;
                    }
                }
            }
        }

        return null;
    }
}
