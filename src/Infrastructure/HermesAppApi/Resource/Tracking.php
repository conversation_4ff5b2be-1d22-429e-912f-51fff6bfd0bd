<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\TrackingEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TrackLocation;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Post(
            controller: [TrackingEndpoint::class, 'storeTracking'],
            filters: [
                new Filter(
                    parameterName: 'equipmentExternalId',
                    parameterDescription: 'External ID of the equipment',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Length(min: 1, max: 50),
                    ]
                ),
                new Filter(
                    parameterName: 'tourExternalId',
                    parameterDescription: 'External ID of the tour',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Length(min: 1, max: 100),
                    ]
                ),
            ],
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: null,
    tag: 'Tracking',
)]
class Tracking
{
    /**
     * @var array<TrackLocation>
     */
    #[Assert\All([
        new Assert\Type(type: TrackLocation::class),
    ])]
    public array $locations;
}
