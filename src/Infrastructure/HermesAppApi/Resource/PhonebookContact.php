<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\HermesAppApi\HttpEndpoint\PhonebookContactEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [PhonebookContactEndpoint::class, 'getCollection'],
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'phonebookContactListItem',
        ),
    ],
    identifier: null,
    tag: 'phonebook',
    security: 'is_granted("'.UserRole::ROLE_DRIVER->value.'")',
)]
class PhonebookContact
{
    #[SerializedName('first_name')]
    public string $firstName;
    #[SerializedName('last_name')]
    public string $lastName;
    #[SerializedName('country_prefix')]
    public string $countryPrefix;
    #[SerializedName('phone_number')]
    public string $phoneNumber;
    #[SerializedName('emergency_contact')]
    public bool $emergencyContact;
    public ?string $description;
}
