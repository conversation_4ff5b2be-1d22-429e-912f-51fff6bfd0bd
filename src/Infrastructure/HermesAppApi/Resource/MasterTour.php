<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\MasterTourEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\MasterTour\MastertourProgressCollection;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\MasterTour\MastertourTemplate;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Get(
            controller: [MasterTourEndpoint::class, 'getByExternalId'],
            uriTemplate: '/mastertour/{externalId}',
            pathParameters: [
                new PathParameter(
                    name: 'externalId',
                    type: 'string',
                    description: 'External ID',
                    constraint: '.+',
                ),
            ],
            output: MastertourTemplate::class,
        ),
        new Post(
            controller: [MasterTourEndpoint::class, 'storeProgress'],
            uriTemplate: '/mastertour-progress',
            input: MastertourProgressCollection::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
    ],
    identifier: null,
    tag: 'Master Tour',
)]
class MasterTour
{
}
