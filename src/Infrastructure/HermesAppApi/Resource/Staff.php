<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\StaffEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\InterruptionBookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroupTemplate;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Routing\Requirement\Requirement;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Patch(
            controller: [StaffEndpoint::class, 'interruptionTaskgroup'],
            summary: 'Complete staff interruption taskgroup (async).',
            uriTemplate: '/staff/interruption/{clientUuid}/taskgroup', pathParameters: [
                new PathParameter(
                    name: 'clientUuid',
                    type: 'string',
                    description: 'The id of the interruption created before.',
                    constraint: Requirement::UUID,
                ),
            ],
            input: TaskGroupTemplate::class,
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [StaffEndpoint::class, 'interruptionBooking'],
            summary: 'create a new interruption/end an existing interruption for staff with interruption template {uuid} (async).',
            uriTemplate: '/staff/interruption/{uuid}/booking', pathParameters: [
                new PathParameter(
                    name: 'uuid',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            input: InterruptionBookingRequest::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
        new Post(
            controller: [StaffEndpoint::class, 'focus'],
            summary: 'staff is currently active in Hermes App.',
            uriTemplate: '/staff/focus',
            requestType: ContentType::EMPTY,
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'Staff',
)]
class Staff
{
}
