<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\FeedbackEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [FeedbackEndpoint::class, 'history'],
            pagination: Pagination::NONE,
        ),
        new Post(
            controller: [FeedbackEndpoint::class, 'create'],
            summary: 'Submit a feedback (async).',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: null,
    tag: 'Feedback',
)]
class Feedback
{
    #[Assert\Length(max: 65000)]
    public ?string $comment = null;

    #[SerializedName('log_file')]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    public string $logFile;

    #[SerializedName('screenshot_file')]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    public string $screenshotFile;

    public static function fromFeedbackEntity(\App\Domain\Entity\Feedback $feedback): self
    {
        $resource = new self();
        $resource->comment = $feedback->getComment();
        $resource->logFile = $feedback->getLogFile();
        $resource->screenshotFile = $feedback->getScreenshotFile();

        return $resource;
    }
}
