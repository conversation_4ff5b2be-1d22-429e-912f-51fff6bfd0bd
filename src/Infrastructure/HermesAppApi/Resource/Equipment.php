<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\Equipment as EquipmentEntity;
use App\Domain\Entity\SessionEquipment;
use App\Infrastructure\HermesAppApi\Documentation\EquipmentEndpointDocumentation;
use App\Infrastructure\HermesAppApi\HttpEndpoint\EquipmentEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingResponse;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\InterruptionBookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Equipment\ConnectedDeviceDto;
use App\Infrastructure\HermesAppApi\Resource\Dto\Equipment\EquipmentStatus;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroupTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\RuleProcessor;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\TaskGroup;
use App\Infrastructure\HermesAppApi\Service\MapContext;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [EquipmentEndpoint::class, 'list'],
            summary: EquipmentEndpointDocumentation::GET_LIST_SUMMARY,
            description: EquipmentEndpointDocumentation::GET_LIST_DESCRIPTION,
            queryParameters: [
                new QueryParameter(
                    name: 'availability',
                    type: 'string',
                    description: 'All or available',
                    enumValues: [
                        'all',
                        'available',
                    ],
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_LIST]],
        ),
        new GetCollection(
            controller: [EquipmentEndpoint::class, 'search'],
            summary: EquipmentEndpointDocumentation::GET_SEARCH_SUMMARY,
            description: EquipmentEndpointDocumentation::GET_SEARCH_DESCRIPTION,
            uriTemplate: '/equipment-list-extended',
            queryParameters: [
                new QueryParameter(
                    name: 'equipment_type',
                    type: 'string',
                    multipleValues: true,
                ),
                new QueryParameter(
                    name: 'equipment_type_category',
                    type: 'string',
                    multipleValues: true,
                ),
                new QueryParameter(
                    name: 'query',
                    type: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_LIST]],
        ),
        new Get(
            controller: [EquipmentEndpoint::class, 'item'],
            summary: EquipmentEndpointDocumentation::GET_ITEM_SUMMARY,
            description: EquipmentEndpointDocumentation::GET_ITEM_DESCRIPTION,
            pathParameters: [
                new PathParameter(
                    name: 'uuid',
                    type: 'string',
                    description: 'Equipment UUID',
                    constraint: Requirement::UUID,
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_ITEM]],
        ),
        new Post(
            controller: [EquipmentEndpoint::class, 'booking'],
            summary: EquipmentEndpointDocumentation::BOOKING_SUMMARY,
            description: EquipmentEndpointDocumentation::BOOKING_DESCRIPTION,
            uriTemplate: '/equipment/{uuid}/booking',
            pathParameters: [
                new PathParameter(
                    name: 'uuid',
                    type: 'string',
                    description: 'Equipment UUID',
                    constraint: Requirement::UUID,
                ),
            ],
            input: BookingRequest::class,
            output: BookingResponse::class,
            successHttpCode: 200,
        ),
        new Patch(
            controller: [EquipmentEndpoint::class, 'taskgroup'],
            summary: EquipmentEndpointDocumentation::COMPLETE_TASKGROUP_SUMMARY,
            description: EquipmentEndpointDocumentation::COMPLETE_TASKGROUP_DESCRIPTION,
            uriTemplate: '/equipment/{uuid}/taskgroup',
            pathParameters: [
                new PathParameter(
                    name: 'uuid',
                    type: 'string',
                    description: 'Equipment UUID',
                    constraint: Requirement::UUID,
                ),
            ],
            input: Dto\Input\TaskGroup::class,
            responseType: ContentType::EMPTY,
        ),
        new Patch(
            controller: [EquipmentEndpoint::class, 'interruptionTaskgroup'],
            summary: EquipmentEndpointDocumentation::INTERRUPTION_TASKGROUP_SUMMARY,
            description: EquipmentEndpointDocumentation::INTERRUPTION_TASKGROUP_DESCRIPTION,
            uriTemplate: '/equipment/interruption/{clientUuid}/taskgroup',
            pathParameters: [
                new PathParameter(
                    name: 'clientUuid',
                    type: 'string',
                    description: 'Client UUID',
                    constraint: Requirement::UUID,
                ),
            ],
            omitDefaultQueryParameters: true,
            input: TaskGroupTemplate::class,
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [EquipmentEndpoint::class, 'interruptionBooking'],
            summary: EquipmentEndpointDocumentation::INTERRUPTION_BOOKING_SUMMARY,
            description: EquipmentEndpointDocumentation::INTERRUPTION_BOOKING_DESCRIPTION,
            uriTemplate: '/equipment/interruption/{uuid}/booking',
            pathParameters: [
                new PathParameter(
                    name: 'uuid',
                    type: 'string',
                    description: 'Equipment UUID',
                    constraint: Requirement::UUID,
                ),
            ],
            input: InterruptionBookingRequest::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
    ],
    identifier: 'uuid',
    tag: 'Equipment',
)]
class Equipment
{
    public const string GROUP_LIST = 'equipment:list';
    public const string GROUP_ITEM = 'equipment:item';

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public string $uuid;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public string $name;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public EquipmentStatus $status;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public string $category;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public ?int $height;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public ?int $width;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public ?int $length;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public ?int $weight;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    #[SerializedName('minimum_load')]
    public ?int $minimumLoad;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public ?int $overload;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    #[SerializedName('total_permissible_weight')]
    public ?int $totalPermissibleWight;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    #[SerializedName('max_axle_load')]
    public ?int $maxAxleLoad;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    #[SerializedName('license_plate')]
    public string $licensePlate;

    #[Groups([self::GROUP_LIST])]
    public bool $blocked = false;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('block_message')]
    public ?string $blockMessage = null;

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('external_id')]
    public ?string $externalId;

    /**
     * @var array<ConnectedDeviceDto>
     */
    #[Groups([self::GROUP_ITEM])]
    public array $devices = [];

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('ddd_file_required')]
    public bool $dddFileRequired = false;

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('last_ddd_file_upload')]
    public ?\DateTimeImmutable $lastDddFileUpload = null;

    /**
     * @var array<TaskGroup>
     */
    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('taskgroups')]
    public array $taskGroups = [];

    public static function listDtoFromEntity(EquipmentEntity $equipment, MapContext $context): self
    {
        $blocked = self::determineBlockedStatus($equipment, $context);

        $self = new self();
        $self->uuid = $equipment->getId();
        $self->name = $equipment->getLicensePlate();
        $self->status = self::determineEquipmentStatus($equipment, $context);
        $self->category = $equipment->getType()->getCategory()->value;
        $self->height = $equipment->getHeight();
        $self->width = $equipment->getWidth();
        $self->length = $equipment->getLength();
        $self->weight = $equipment->getWeight();
        $self->minimumLoad = $equipment->getMinimumLoad();
        $self->overload = $equipment->getOverload();
        $self->totalPermissibleWight = $equipment->getTotalPermissibleWeight();
        $self->maxAxleLoad = $equipment->getMaxAxleLoad();
        $self->licensePlate = $equipment->getLicensePlate();
        $self->blocked = $blocked;
        $self->blockMessage = ($blocked) ? '{{module/equipment/blocked/different_device_usage_text}}' : null;

        return $self;
    }

    public static function itemDtoFromEntity(EquipmentEntity $equipment, MapContext $context): self
    {
        $currentDeviceUid = $context->getDeviceContext()->getDeviceId();
        $sessionEquipment = $equipment->getSessionEquipments()
            ->filter(
                fn (SessionEquipment $sessionEquipment): bool
                    => $sessionEquipment->getSession()->getDeviceUniqueId() === $currentDeviceUid
                       && $sessionEquipment->getEquipment() === $equipment
                       && null === $sessionEquipment->getEnd()
                       && null === $sessionEquipment->getSession()->getEnd()
            )
            ->first();

        $taskGroups = [];

        if ($sessionEquipment) {
            $taskGroups = array_map(
                static fn (\App\Domain\Entity\TaskGroup $taskGroup): TaskGroup => TaskGroup::fromEntity($taskGroup, $context),
                $sessionEquipment->getTaskGroups()->toArray(),
            );

            $taskGroups = RuleProcessor::processTaskGroupsRules($taskGroups);
        }

        $devices = [];
        foreach ($equipment->getConnectedDevices() as $connectedDevice) {
            $devices[] = ConnectedDeviceDto::fromEntity($connectedDevice);
        }

        $status = self::determineEquipmentStatus($equipment, $context);

        $resource = new self();
        $resource->uuid = $equipment->getId();
        $resource->externalId = $equipment->getExternalId();
        $resource->name = $equipment->getLicensePlate();
        $resource->status = $status;
        $resource->category = $equipment->getType()->getCategory()->value;
        $resource->height = $equipment->getHeight();
        $resource->width = $equipment->getWidth();
        $resource->length = $equipment->getLength();
        $resource->weight = $equipment->getWeight();
        $resource->minimumLoad = $equipment->getMinimumLoad();
        $resource->overload = $equipment->getOverload();
        $resource->totalPermissibleWight = $equipment->getTotalPermissibleWeight();
        $resource->maxAxleLoad = $equipment->getMaxAxleLoad();
        $resource->licensePlate = $equipment->getLicensePlate();
        $resource->devices = $devices;
        $resource->taskGroups = $taskGroups;
        $resource->dddFileRequired = $equipment->dddFileUploadRequired();
        $resource->lastDddFileUpload = $equipment->getLastDddFileUpload();

        return $resource;
    }

    private static function determineEquipmentStatus(EquipmentEntity $equipment, MapContext $context): EquipmentStatus
    {
        $currentDeviceUid = $context->getDeviceContext()->getDeviceId();

        foreach ($equipment->getSessionEquipments() as $sessionEquipment) {
            if (
                null === $sessionEquipment->getEnd()
                && $sessionEquipment->getSession()->getDeviceUniqueId() === $currentDeviceUid
            ) {
                return EquipmentStatus::EQUIPPED;
            }
        }

        return EquipmentStatus::UNEQUIPPED;
    }

    private static function determineBlockedStatus(EquipmentEntity $equipment, MapContext $context): bool
    {
        $currentDeviceUid = $context->getDeviceContext()->getDeviceId();

        foreach ($equipment->getSessionEquipments() as $sessionEquipment) {
            if (
                null === $sessionEquipment->getEnd()
                && \App\Domain\Entity\Enum\Status\EquipmentStatus::IN_USE === $equipment->getStatus()
                && $sessionEquipment->getSession()->getDeviceUniqueId() !== $currentDeviceUid
            ) {
                return true;
            }
        }

        return false;
    }
}
