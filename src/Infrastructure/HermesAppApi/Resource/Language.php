<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\LanguageEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Language\Translation;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [LanguageEndpoint::class, 'list'],
            summary: 'Get list of possible languages.',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_LIST]],
        ),
        new Get(
            controller: [LanguageEndpoint::class, 'language'],
            summary: 'Get the data for one of the possible languages.',
            normalizationContext: ['groups' => [self::GROUP_ITEM]],
        ),
    ],
    identifier: 'iso',
    tag: 'Language',
)]
class Language
{
    public const string GROUP_LIST = 'language:list';
    public const string GROUP_ITEM = 'language:item';

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public string $iso;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    public string $name;

    #[Groups([self::GROUP_LIST, self::GROUP_ITEM])]
    #[SerializedName('last_update')]
    public \DateTimeImmutable $lastUpdate;

    /**
     * @var array<Translation>
     */
    #[Groups([self::GROUP_ITEM])]
    public array $translations;
}
