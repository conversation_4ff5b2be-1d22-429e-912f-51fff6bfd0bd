<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\DataProtectionNoticeEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\ContentType;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Get(
            controller: [DataProtectionNoticeEndpoint::class, 'get'],
            uriTemplate: '/data-protection-notice/{locale}',
            pathParameters: [
                new PathParameter(
                    name: 'locale',
                    type: 'string',
                    description: 'Locale of the data protection notice',
                    constraint: '[a-z]{2}(_[A-Z]{2})?',
                ),
            ],
            responses: [
                new Response(
                    httpCode: 200,
                    description: 'Markdown format of data protection notice',
                    mimeType: 'text/markdown',
                ),
            ],
            responseType: ContentType::BINARY
        ),
    ],
    identifier: null,
    tag: 'Data Protection Notice',
)]
class DataProtectionNotice
{
}
