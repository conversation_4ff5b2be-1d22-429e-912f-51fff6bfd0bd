<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\Equipment;
use App\Domain\Entity\Staff;
use App\Infrastructure\HermesAppApi\Documentation\DeviceMessageDocumentation;
use App\Infrastructure\HermesAppApi\HttpEndpoint\DeviceMessageEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [DeviceMessageEndpoint::class, 'getList'],
            summary: DeviceMessageDocumentation::GET_LIST_SUMMARY,
            uriTemplate: '/device-message',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [DeviceMessage::GROUP_READ]],
        ),
    ],
    tag: 'Device Message',
)]
class DeviceMessageThread
{
    #[Groups([DeviceMessage::GROUP_READ])]
    public string $id;

    #[Groups([DeviceMessage::GROUP_READ])]
    #[SerializedName('recipient_user_id')]
    public ?string $recipientUserId;

    #[Groups([DeviceMessage::GROUP_READ])]
    public string $recipient;

    #[Groups([DeviceMessage::GROUP_READ])]
    #[SerializedName('updated_at')]
    public \DateTimeImmutable $updatedAt;

    #[Groups([DeviceMessage::GROUP_READ])]
    #[SerializedName('has_unread_messages')]
    public bool $hasUnreadMessages;

    /**
     * @var array<DeviceMessage> $messages
     */
    #[Groups([DeviceMessage::GROUP_READ])]
    public array $messages;

    public static function fromEntity(\App\Domain\Entity\DeviceMessageThread $entity): self
    {
        $recipientUserId = null;
        $recipient = '';

        $target = $entity->getTarget();

        if ($target instanceof Staff) {
            $recipientUserId = $target->getUser()->getId();
            $recipient = $target->getUser()->getFirstname().' '.$target->getUser()->getLastname();
        }

        if ($target instanceof Equipment) {
            $recipient = $target->getLicensePlate();
        }

        $resource = new self();
        $resource->id = $entity->getId();
        $resource->recipientUserId = $recipientUserId;
        $resource->recipient = $recipient;
        $resource->updatedAt = $entity->getModifiedAt();
        $resource->hasUnreadMessages = $entity->hasUnreadMessagesForDevice();
        $resource->messages = array_map(DeviceMessage::fromEntity(...), $entity->getMessages()->toArray());

        return $resource;
    }
}
