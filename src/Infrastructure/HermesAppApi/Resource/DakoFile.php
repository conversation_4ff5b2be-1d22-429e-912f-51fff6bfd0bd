<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\DakoFileEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Routing\Requirement\Requirement;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Put(
            controller: [DakoFileEndpoint::class, 'store'],
            summary: 'Upload a file to a for a Dako process.',
            uriTemplate: '/dako-process/file/{dakoProcessId}/{fileName}',
            pathParameters: [
                new PathParameter(
                    name: 'dakoProcessId',
                    type: 'string',
                    description: 'The process, the file belongs to',
                    constraint: Requirement::UUID,
                ),
                new PathParameter(
                    name: 'fileName',
                    type: 'string',
                    description: 'The name of the file',
                    constraint: Requirement::CATCH_ALL,
                ),
            ],
            queryParameters: [
                new QueryParameter(
                    name: 'fileNumber',
                    type: 'integer',
                    description: 'number of the currently uploaded file (starting with 1)',
                    required: true,
                ),
                new QueryParameter(
                    name: 'totalFiles',
                    type: 'integer',
                    description: 'total amount of files to be uploaded (should not change during process)',
                    required: true,
                ),
            ],
            requestType: ContentType::BINARY,
            requestDescription: 'Binary file contents',
            responseDescription: 'File uploaded',
            responseType: ContentType::EMPTY,
            successHttpCode: 201,
        ),
        new Get(
            controller: [DakoFileEndpoint::class, 'retrieve'],
            summary: 'Download a file for a Dako process.',
            uriTemplate: '/dako-process/file/{dakoProcessId}/{fileNumber}',
            pathParameters: [
                new PathParameter(
                    name: 'dakoProcessId',
                    type: 'string',
                    description: 'The id of the process',
                    constraint: Requirement::UUID,
                ),
                new PathParameter(
                    name: 'fileNumber',
                    type: 'integer',
                    description: 'the index-number of the file (1 for first file)',
                ),
            ],
            responseDescription: 'Binary file contents',
            responseType: ContentType::BINARY,
        ),
    ],
    identifier: null,
    tag: 'Dako Session',
)]
class DakoFile
{
}
