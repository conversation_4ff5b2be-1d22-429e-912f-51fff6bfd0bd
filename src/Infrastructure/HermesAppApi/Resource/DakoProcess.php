<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\DakoProcessEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\DakoFailResponse;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Dako\DakoApduRequestDto;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Dako\DakoAtrRequestDto;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Dako\DakoApduResponseDto;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Dako\DakoAtrResponseDto;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Dako\DakoProcessDto;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use Psr\Log\LogLevel;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Delete(
            controller: [DakoProcessEndpoint::class, 'close'],
            name: 'dako-session-close',
            summary: 'Close a new session for dako',
            responses: [
                new Response(
                    httpCode: 409,
                    description: 'Dako Service Failed',
                    output: DakoFailResponse::class,
                ),
            ],
            responseCodesLogLevel: [409 => LogLevel::INFO]
        ),
        new Delete(
            controller: [DakoProcessEndpoint::class, 'abort'],
            name: 'dako-session-abort',
            summary: 'abort a process for dako',
            uriTemplate: '/dako-process/abort/{dakoProcessId}',
            pathParameters: [
                new PathParameter(
                    name: 'dakoProcessId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
        ),
        new Get(
            controller: [DakoProcessEndpoint::class, 'get'],
            summary: 'Get an overview about the process',
            output: DakoProcessDto::class,
        ),
        new Post(
            controller: [DakoProcessEndpoint::class, 'start'],
            name: 'dako-session-start',
            summary: 'Get an ATR and create a new session for dako',
            input: DakoAtrRequestDto::class,
            responses: [
                new Response(
                    httpCode: 409,
                    description: 'Dako Service Failed',
                    output: DakoFailResponse::class,
                ),
            ],
            output: DakoAtrResponseDto::class,
            responseCodesLogLevel: [409 => LogLevel::INFO]
        ),

        new Patch(
            controller: [DakoProcessEndpoint::class, 'process'],
            name: 'dako-session-process',
            summary: 'Process a session for dako by exchanging APDUs',
            uriTemplate: '/dako-process/{dakoProcessId}',
            pathParameters: [
                new PathParameter(
                    name: 'dakoProcessId',
                    type: 'string',
                    description: 'Dako Process ID',
                    constraint: Requirement::UUID,
                ),
            ],
            input: DakoApduRequestDto::class,
            responses: [
                new Response(
                    httpCode: 409,
                    description: 'Dako Service Failed',
                    output: DakoFailResponse::class,
                ),
            ],
            output: DakoApduResponseDto::class,
            responseCodesLogLevel: [409 => LogLevel::INFO]
        ),
    ],
    identifier: 'dakoProcessId',
    tag: 'Dako Session',
)]
class DakoProcess
{
    #[Assert\Uuid]
    #[SerializedName('dako_process_id')]
    public string $dakoProcessId;
}
