<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\TaskGroupEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingRequest;
use Pre<PERSON>ero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Routing\Requirement\Requirement;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Post(
            controller: [TaskGroupEndpoint::class, 'booking'],
            summary: 'Booking start of an existing taskGroup below an order or a tour (async).',
            uriTemplate: '/taskgroup/{taskGroupId}/booking',
            pathParameters: [
                new PathParameter(
                    name: 'taskGroupId',
                    type: 'string',
                    description: 'Task Group ID',
                    constraint: Requirement::UUID,
                ),
            ],
            input: BookingRequest::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
    ],
    identifier: null,
    tag: 'TaskGroup',
)]
class TaskGroup
{
}
