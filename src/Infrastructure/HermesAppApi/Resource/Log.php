<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\LogEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Log\LogItem;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Post(
            controller: [LogEndpoint::class, 'appLog'],
            summary: 'Stores a log entry from application to Argus Grafana Loki.',
            uriTemplate: '/log/app',
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
        new Post(
            controller: [LogEndpoint::class, 'navigationLog'],
            summary: 'Stores a log entry from navigation application to Argus Grafana Loki.',
            uriTemplate: '/log/navigation',
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'Logging',
)]
class Log
{
    /**
     * @var array<LogItem>
     */
    #[Assert\All([
        new Assert\Type(type: LogItem::class),
    ])]
    public array $logs;
}
