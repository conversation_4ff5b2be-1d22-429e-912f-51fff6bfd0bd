<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\PointOfInterest as PointOfInterestEntity;
use App\Infrastructure\HermesAppApi\HttpEndpoint\PointOfInterestEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [PointOfInterestEndpoint::class, 'getList'],
            summary: 'Get list of Point of Interests.',
            uriTemplate: '/poi',
            pagination: Pagination::NONE
        ),
    ],
    identifier: null,
    tag: 'Point of Interest',
)]
class PointOfInterest
{
    public string $name;
    public float $longitude;
    public float $latitude;
    public string $icon;

    public static function fromEntity(PointOfInterestEntity $pointOfInterest): self
    {
        $resource = new self();
        $resource->name = $pointOfInterest->getName();
        $resource->longitude = $pointOfInterest->getLongitude();
        $resource->latitude = $pointOfInterest->getLatitude();
        $resource->icon = $pointOfInterest->getIcon();

        return $resource;
    }
}
