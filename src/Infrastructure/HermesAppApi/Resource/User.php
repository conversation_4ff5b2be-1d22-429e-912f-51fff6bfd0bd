<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\Enum\Colormode;
use App\Domain\Entity\Enum\Locale;
use App\Domain\Entity\Enum\OptionButtonLocation;
use App\Domain\Entity\SessionUser;
use App\Infrastructure\HermesAppApi\HttpEndpoint\UserEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingResponse;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroup;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\UserPasswordChange;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\UserProfileChange;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\RuleProcessor;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\User\UserSession;
use App\Infrastructure\HermesAppApi\Service\MapContext;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [UserEndpoint::class, 'list'],
            summary: 'Gets a list of users, that are currently logged in the active session of the device-id.',
            pagination: Pagination::NONE,
        ),
        new Get(
            controller: [UserEndpoint::class, 'profile'],
            summary: 'Gets the profile-data of the user and the user\'s taskGroups.',
            uriTemplate: '/user/me',
            normalizationContext: ['groups' => [self::GROUP_ITEM]],
        ),
        new Patch(
            controller: [UserEndpoint::class, 'changeProfile'],
            summary: 'Updates the allowed data of a user. (async).',
            uriTemplate: '/user/me',
            input: UserProfileChange::class,
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [UserEndpoint::class, 'startSession'],
            summary: 'Starts a user session and attaches it to the device session related to the device-uuid in the header.',
            uriTemplate: '/user/start-session',
            requestType: ContentType::EMPTY,
            output: UserSession::class,
            successHttpCode: 200,
        ),
        new Post(
            controller: [UserEndpoint::class, 'endSession'],
            summary: 'Ends a user session.',
            uriTemplate: '/user/end-session',
            requestType: ContentType::EMPTY,
            output: BookingResponse::class,
            successHttpCode: 200,
        ),
        new Patch(
            controller: [UserEndpoint::class, 'completeTaskGroup'],
            summary: 'Completes the taskGroups of an active sessionUser (async per queue).',
            uriTemplate: '/user/taskgroup',
            input: TaskGroup::class,
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [UserEndpoint::class, 'changePassword'],
            summary: 'Changes the password of the user.',
            description: 'Only allowed if the user has the necessary flag for password change',
            uriTemplate: '/user/change-password',
            input: UserPasswordChange::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'User',
)]
class User
{
    public const string GROUP_ITEM = 'user:item';
    public const string GROUP_LIST = 'user:list';

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    public string $uuid;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    public int $status;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('first_name')]
    public string $firstName;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('last_name')]
    public string $lastName;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('colormode')]
    public ?Colormode $colorMode;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    public ?Locale $language;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('option_button_location')]
    public ?OptionButtonLocation $optionButtonLocation;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('last_update')]
    public ?\DateTimeImmutable $lastUpdate;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('last_logged_in')]
    public ?\DateTimeImmutable $lastLoggedIn;

    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('last_device_id')]
    public ?string $lastDeviceId;

    /**
     * @var array<Dto\Output\Common\TaskGroup>|null
     */
    #[Groups([self::GROUP_ITEM, self::GROUP_LIST])]
    #[SerializedName('taskgroups')]
    public ?array $taskGroups = null;

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('ddd_file_required')]
    public bool $dddFileRequired = false;

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('last_ddd_file_upload')]
    public ?\DateTimeImmutable $lastDddFileUpload = null;

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('password_reset_required')]
    public bool $passwordResetRequired = false;

    public static function fromSessionUser(SessionUser $sessionUser, MapContext $context): self
    {
        $user = $sessionUser->getUser();

        $taskGroups = array_map(
            static fn (\App\Domain\Entity\TaskGroup $taskGroup): Dto\Output\Common\TaskGroup
                => Dto\Output\Common\TaskGroup::fromEntity($taskGroup, $context),
            $sessionUser->getTaskGroups()->toArray(),
        );

        $taskGroups = RuleProcessor::processTaskGroupsRules($taskGroups);

        $resource = new self();
        $resource->uuid = $user->getId();
        $resource->status = $user->getStatus();
        $resource->firstName = $user->getFirstname();
        $resource->lastName = $user->getLastname();
        $resource->colorMode = $user->getUserSetting()?->getColormode();
        $resource->language = $user->getUserSetting()?->getLanguage() ?: Locale::en_GB;
        $resource->optionButtonLocation = $user->getUserSetting()?->getOptionButtonLocation();
        $resource->lastUpdate = $user->getModifiedAt();
        $resource->lastLoggedIn = $user->getLastLogin();
        $resource->lastDeviceId = null;
        $resource->taskGroups = $taskGroups;
        $resource->dddFileRequired = $user->getStaff()?->dddFileUploadRequired() ?? false;
        $resource->lastDddFileUpload = $user->getStaff()?->getLastDddFileUpload();
        $resource->passwordResetRequired = $user->isPasswordResetRequired();

        return $resource;
    }
}
