<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\Enum\Status\TourStatus;
use App\Domain\Entity\Enum\TrackingProtection;
use App\Infrastructure\HermesAppApi\HttpEndpoint\TourEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingResponse;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\InterruptionBookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroup;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroupTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Termination;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\AdditionalInformation;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\TerminationHistory;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\AdditionalService;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\EquipmentReference;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\TerminationTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\TourSummary;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new GetCollection(
            controller: [TourEndpoint::class, 'getTerminations'],
            summary: 'Gets a list of sent and processed tour-terminations of a tour with {tourId}.',
            uriTemplate: '/tour/{tourId}/termination', pathParameters: [
                new PathParameter(
                    name: 'tourId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            pagination: Pagination::NONE,
            output: TerminationHistory::class,
        ),
        new Post(
            controller: [TourEndpoint::class, 'terminateTour'],
            summary: 'Terminates a tour with the {tourId} completely (sync).',
            uriTemplate: '/tour/{tourId}/termination', pathParameters: [
                new PathParameter(
                    name: 'tourId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            input: Termination::class,
            output: BookingResponse::class,
            successHttpCode: 200,
        ),
        new GetCollection(
            controller: [TourEndpoint::class, 'getList'],
            summary: 'Gets a list of tours with shortened content.',
            pagination: Pagination::NONE,
            output: TourSummary::class,
        ),
        new Get(
            controller: [TourEndpoint::class, 'getById'],
            summary: 'Get the detailed information about a tour.',
            uriTemplate: '/tour/{uuid}', ),
        new Post(
            controller: [TourEndpoint::class, 'booking'],
            summary: 'Starts or ends a tour (sync).',
            uriTemplate: '/tour/{tourId}/booking', pathParameters: [
                new PathParameter(
                    name: 'tourId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            input: BookingRequest::class,
            output: BookingResponse::class,
            successHttpCode: 200,
        ),
        new Patch(
            controller: [TourEndpoint::class, 'completeTaskgroup'],
            summary: 'Fills an existing TaskGroup, that lies directly below a tour (async).',
            uriTemplate: '/tour/{tourId}/taskgroup', pathParameters: [
                new PathParameter(
                    name: 'tourId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            input: TaskGroup::class,
            responseType: ContentType::EMPTY,
        ),
        new Patch(
            controller: [TourEndpoint::class, 'interruptionTaskgroup'],
            summary: 'Fill the taskgroups of a created tour-interruption referenced with the app-side created uuid. (async).',
            uriTemplate: '/tour/interruption/{clientUuid}/taskgroup', pathParameters: [
                new PathParameter(
                    name: 'clientUuid',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            omitDefaultQueryParameters: true,
            input: TaskGroupTemplate::class,
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [TourEndpoint::class, 'interruptionBooking'],
            summary: 'Create a new interruption/end an existing for tour with interruption template {uuid} (async).',
            uriTemplate: '/tour/interruption/{uuid}/booking', pathParameters: [
                new PathParameter(
                    name: 'uuid',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            input: InterruptionBookingRequest::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
    ],
    identifier: 'uuid',
    tag: 'Tour',
)]
class Tour
{
    public string $name;

    public string $uuid;

    #[SerializedName('external_id')]
    public string $externalId;

    public TourStatus $status;

    #[SerializedName('tracking_protection')]
    public TrackingProtection $trackingProtection;

    /**
     * @var string[]
     */
    #[SerializedName('mastertour_templates')]
    public array $mastertourTemplates;

    /**
     * @var array<Order>
     */
    public array $orders;

    /**
     * @var array<EquipmentReference>|null
     */
    public ?array $equipments = null;

    #[SerializedName('last_tour_update')]
    public ?\DateTimeImmutable $lastTourUpdate = null;

    /**
     * @var array<TerminationTemplate>|null
     */
    #[SerializedName('termination_templates')]
    public ?array $terminationTemplates = null;

    /**
     * @var array<AdditionalService>|null
     */
    #[SerializedName('additional_service_templates')]
    public ?array $additionalServiceTemplates = null;

    /**
     * @var array<Dto\Output\Common\TaskGroup>|null
     */
    #[SerializedName('taskgroups')]
    public ?array $taskGroups = null;

    /**
     * @var array<AdditionalInformation>
     */
    #[SerializedName('additional_informations')]
    public array $additionalInformation = [];
}
