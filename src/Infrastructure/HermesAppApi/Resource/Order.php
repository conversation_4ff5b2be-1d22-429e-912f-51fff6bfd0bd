<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Domain\Entity\Enum\Status\OrderStatus;
use App\Infrastructure\HermesAppApi\HttpEndpoint\OrderEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingRequest;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Order\Note;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroup;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\Termination;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\AdditionalInformation;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Order\NoteHistory;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\TerminationHistory;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\NoteTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\OrderTaskGroup;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\TerminationTemplate;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Get(
            controller: [OrderEndpoint::class, 'getOrder'],
            summary: 'Get a single order by its id.',
        ),
        new GetCollection(
            controller: [OrderEndpoint::class, 'listNotes'],
            summary: 'Get a list of notes, that have been processed for the order.',
            uriTemplate: '/order/{orderId}/note', pathParameters: [
                new PathParameter(
                    name: 'orderId',
                    type: 'string',
                    description: 'The id of order above.',
                    constraint: Requirement::UUID,
                ),
            ],
            pagination: Pagination::NONE,
            output: NoteHistory::class,
        ),
        new Post(
            controller: [OrderEndpoint::class, 'bookOrder'],
            summary: 'Starts or ends an order (async).',
            uriTemplate: '/order/{orderId}/booking', pathParameters: [
                new PathParameter(
                    name: 'orderId',
                    type: 'string',
                    description: 'The id of order above.',
                    constraint: Requirement::UUID,
                ),
            ],
            input: BookingRequest::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
        new Patch(
            controller: [OrderEndpoint::class, 'completeTaskgroup'],
            summary: 'Fills an existing taskgroup of an order with the uuid, set in the url. (async).',
            uriTemplate: '/order/{orderId}/taskgroup', input: TaskGroup::class,
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [OrderEndpoint::class, 'terminateOrder'],
            summary: 'Terminates a single order with its contained taskGroups (async).',
            uriTemplate: '/order/{orderId}/termination', pathParameters: [
                new PathParameter(
                    name: 'orderId',
                    type: 'string',
                    description: 'The id of order above.',
                    constraint: Requirement::UUID,
                ),
            ],
            input: Termination::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
        new Post(
            controller: [OrderEndpoint::class, 'createNote'],
            summary: 'Creates a note related to an order by a template (async).',
            uriTemplate: '/order/{orderId}/note', pathParameters: [
                new PathParameter(
                    name: 'orderId',
                    type: 'string',
                    description: 'The id of order above.',
                    constraint: Requirement::UUID,
                ),
            ],
            input: Note::class,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
        new GetCollection(
            controller: [OrderEndpoint::class, 'getTerminations'],
            summary: 'Gets a list of send and processed terminations of an order with {orderId}.',
            uriTemplate: '/order/{orderId}/termination', pathParameters: [
                new PathParameter(
                    name: 'orderId',
                    type: 'string',
                    description: 'The id of order above.',
                    constraint: Requirement::UUID,
                ),
            ],
            pagination: Pagination::NONE,
            output: TerminationHistory::class,
        ),
        new Post(
            controller: [OrderEndpoint::class, 'focus'],
            summary: 'Order is currently opened in Hermes App.',
            uriTemplate: '/order/{orderId}/focus', pathParameters: [
                new PathParameter(
                    name: 'orderId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            requestType: ContentType::EMPTY,
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
    ],
    identifier: 'orderId',
    tag: 'Order',
)]
class Order
{
    #[SerializedName('uuid')]
    public string $orderId;

    public string $name;

    #[SerializedName('type')]
    public string $orderTypeLabel;

    public OrderStatus $status;

    /**
     * @var array<AdditionalInformation>
     */
    #[SerializedName('additional_informations')]
    public array $additionalInformation = [];

    /**
     * @var array<TerminationTemplate>
     */
    #[SerializedName('termination_templates')]
    public array $terminationTemplates = [];

    /**
     * @var array<NoteTemplate>
     */
    #[SerializedName('note_templates')]
    public array $noteTemplates = [];

    /**
     * @var array<OrderTaskGroup>
     */
    #[SerializedName('taskgroups')]
    public array $taskGroups = [];
}
