<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Resource;

use App\Infrastructure\HermesAppApi\HttpEndpoint\VehicleInspectionEndpoint;
use App\Infrastructure\HermesAppApi\Resource\Dto\VehicleInspection\EquipmentComponentState;
use App\Infrastructure\HermesAppApi\Resource\Dto\VehicleInspection\VehicleInspectionConfig;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'hermes_app',
    operations: [
        new Get(
            controller: [VehicleInspectionEndpoint::class, 'template'],
            uriTemplate: '/vehicle-inspection-report/{equipmentId}/template', pathParameters: [
                new PathParameter(
                    name: 'equipmentId',
                    type: 'string',
                    description: 'The ID of the equipment for which the inspection report template is requested.',
                    constraint: Requirement::UUID,
                ),
            ],
            output: VehicleInspectionConfig::class,
        ),
        new Post(
            controller: [VehicleInspectionEndpoint::class, 'submit'],
            uriTemplate: '/vehicle-inspection-report/{equipmentId}', pathParameters: [
                new PathParameter(
                    name: 'equipmentId',
                    type: 'string',
                    description: 'The ID of the equipment for which the inspection report is submitted.',
                    constraint: Requirement::UUID,
                ),
            ],
            denormalizationContext: ['groups' => [self::GROUP_SUBMIT]],
            requestOpenApiSchemaName: 'VehicleInspectionReport',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: 'equipmentId',
    tag: 'Vehicle Inspection Report',
)]
class VehicleInspectionReport
{
    public const string GROUP_SUBMIT = 'vehicle-inspection:submit';

    #[Assert\Uuid]
    public string $equipmentId;

    /**
     * @var array<EquipmentComponentState>
     */
    #[Groups([self::GROUP_SUBMIT])]
    #[SerializedName('components_state')]
    #[Assert\NotNull]
    #[Assert\Type(type: 'array')]
    #[Assert\All([
        new Assert\Type(type: EquipmentComponentState::class),
    ])]
    public array $componentsState;

    #[Assert\Length(max: 65535)]
    #[Groups([self::GROUP_SUBMIT])]
    #[SerializedName('description')]
    public ?string $description = null;

    /**
     * @var array<string>
     */
    #[Groups([self::GROUP_SUBMIT])]
    #[SerializedName('uploaded_files')]
    #[Assert\Type(type: 'array')]
    #[Assert\All([
        new Assert\Type(type: 'string'),
        new Assert\Length(min: 1, max: 255),
    ])]
    public array $uploadedFiles = [];
}
