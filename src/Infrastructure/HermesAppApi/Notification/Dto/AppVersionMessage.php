<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\Notification\Dto;

use App\Infrastructure\Notification\NotificationDtoInterface;
use Symfony\Component\Serializer\Attribute\SerializedName;

readonly class AppVersionMessage implements NotificationDtoInterface
{
    public function __construct(
        #[SerializedName('recommended_version')]
        public string $recommendedVersion,

        public string $title,

        public string $message,

        public string $type = 'app_version',
    ) {
    }

    public function getType(): string
    {
        return $this->type;
    }
}
