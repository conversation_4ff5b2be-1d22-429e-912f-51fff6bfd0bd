<?php

/** @noinspection DuplicatedCode */

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\DtoBuilder;

use App\Domain\Entity\AccessibleTaskGroup;
use App\Domain\Entity\AccessibleTaskRelation;
use App\Domain\Entity\Enum\Status\OrderStatus;
use App\Domain\Entity\Enum\Status\TaskGroupStatus;
use App\Domain\Entity\Order as OrderEntity;
use App\Domain\Entity\TaskRelation;
use App\Domain\Entity\Tour as TourEntity;
use App\Domain\Repository\AccessibleTaskGroupRepository;
use App\Domain\Repository\AccessibleTaskRelationRepository;
use App\Domain\Repository\AccessibleTerminationRepository;
use App\Domain\Repository\AdditionalServiceConfigRepository;
use App\Domain\Repository\EquipmentRepository;
use App\Domain\Repository\LocationRepository;
use App\Domain\Repository\TaskRelationRepository;
use App\Infrastructure\HermesAppApi\Resource\Dto\Location;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\AdditionalInformation;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\Element\Element;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\Rule;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\RuleProcessor;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\Task;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\TaskGroup;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\TaskGroupTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Common\TaskTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\AdditionalService;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\EquipmentReference;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\OrderSummary;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\OrderTask;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\OrderTaskGroup;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\TerminationTemplate;
use App\Infrastructure\HermesAppApi\Resource\Dto\Output\Tour\TourSummary;
use App\Infrastructure\HermesAppApi\Service\LocationFormatService;
use App\Infrastructure\HermesAppApi\Service\MapContext;

class TourSummaryDtoBuilder
{
    private MapContext $context;

    /** @var array<OrderSummary> */
    private array $orders = [];

    /** @var array<TaskGroup|OrderTaskGroup> */
    private array $taskGroups = [];

    /** @var array<string, TaskGroupTemplate> */
    private array $taskGroupTemplates = [];

    /** @var array<string, TerminationTemplate> */
    private array $terminationTemplates = [];

    /** @var array<string, string> */
    private array $locationIds = [];

    /** @var array<string, array<string, array<string>>> */
    private array $locationsMap = [];

    public function __construct(
        private readonly AccessibleTaskGroupRepository $accessibleTaskGroupRepository,
        private readonly AccessibleTaskRelationRepository $accessibleTaskRelationRepository,
        private readonly AccessibleTerminationRepository $accessibleTerminationRepository,
        private readonly AdditionalServiceConfigRepository $additionalServiceConfigRepository,
        private readonly LocationFormatService $locationFormatService,
        private readonly TaskRelationRepository $taskRelationRepository,
        private readonly EquipmentRepository $equipmentRepository,
        private readonly LocationRepository $locationRepository,
    ) {
    }

    public function buildFromTourEntity(TourEntity $tourEntity): TourSummary
    {
        $this->reset();
        $additionalServiceConfig = $this->additionalServiceConfigRepository->findConfigsForTour($tourEntity);

        $this->context = new MapContext();
        $this->context->setLocationFormatService($this->locationFormatService);

        if ([] !== $additionalServiceConfig) {
            $this->context->setAdditionalServiceConfigs($additionalServiceConfig);
        }

        $tourDto = new TourSummary(
            name: $tourEntity->getName(),
            uuid: $tourEntity->getId(),
            externalId: $tourEntity->getExternalId(),
            status: $tourEntity->getStatus(),
            mastertourTemplates: $tourEntity->getMastertours(),
            orders: $this->getOrders($tourEntity),
            equipments: array_map(
                EquipmentReference::fromEntity(...),
                $this->equipmentRepository->getEquipmentsForTour($tourEntity)
            ),
            terminationTemplates: $this->getTerminationTemplates($tourEntity),
            additionalServiceTemplates: $this->getAdditionalServiceTemplates(),
            taskGroups: $this->getTaskGroups($tourEntity),
            additionalInformation: array_map(
                AdditionalInformation::fromAdditionalInformationObject(...),
                $tourEntity->getAdditionalInformation(),
            ),
        );

        $this->processTerminationTemplates();
        $this->processTaskGroups();
        $this->processTaskGroupTemplates();
        $this->processLocations();

        RuleProcessor::processTaskGroupsRules($this->taskGroups);
        RuleProcessor::processTaskGroupsRules($this->taskGroupTemplates);

        return $tourDto;
    }

    /**
     * @return array<OrderSummary>
     */
    private function getOrders(TourEntity $tourEntity): array
    {
        $orders = [];

        foreach ($tourEntity->getOrders() as $orderEntity) { // DB Query
            if (OrderStatus::OBSOLETE === $orderEntity->getStatus()) {
                continue;
            }

            $this->orders[$orderEntity->getId()] = $this->getOrder($orderEntity);
            $orders[] = $this->orders[$orderEntity->getId()];
        }

        return $orders;
    }

    private function getOrder(OrderEntity $orderEntity): OrderSummary
    {
        $locationId = $orderEntity->getLocation()->getId();
        $this->locationIds[$locationId] = $locationId;
        $this->locationsMap[$locationId]['order'][] = $orderEntity->getId();

        $orderTypeName = $orderEntity->getName();
        $orderTypeLabel = $orderTypeName ?? $orderEntity->getType()->value;

        return new OrderSummary(
            uuid: $orderEntity->getId(),
            name: '{{order_name_prefix}} '.$orderEntity->getOrderPosition(),
            orderTypeLabel: $orderTypeLabel,
            status: $orderEntity->getStatus(),
            additionalInformation: array_map(
                AdditionalInformation::fromAdditionalInformationObject(...),
                $orderEntity->getAdditionalInformation(),
            ),
        );
    }

    /**
     * @return array<TerminationTemplate>
     */
    private function getTerminationTemplates(TourEntity $tourEntity): array
    {
        $accessibleTerminationEntities = $this->accessibleTerminationRepository->findActiveByTour($tourEntity); // DB Query
        $terminationTemplates = [];

        foreach ($accessibleTerminationEntities as $accessibleTerminationEntity) {
            $seqNum = $accessibleTerminationEntity->getAccessibleTerminationRelations()->get(0)?->getSequenceNumber() ?: 0;
            $terminationTemplate = $this->terminationTemplates[$accessibleTerminationEntity->getId()] ?? new TerminationTemplate(
                templateUuid: $accessibleTerminationEntity->getId(),
                name: $accessibleTerminationEntity->getText(),
                sequenceNumber: $seqNum,
            );
            $this->terminationTemplates[$accessibleTerminationEntity->getId()] = $terminationTemplate;

            $terminationTemplates[$accessibleTerminationEntity->getId()] = $terminationTemplate;
        }

        return array_values($terminationTemplates);
    }

    /**
     * @return array<AdditionalService>
     */
    private function getAdditionalServiceTemplates(): array
    {
        $ret = [];
        if ($this->context->hasAdditionalServiceConfig()) {
            foreach ($this->context->getAdditionalServiceConfigs() as $additionalServiceConfig) {
                $ret[] = AdditionalService::fromDefaultTask(
                    $additionalServiceConfig->getDefaultTask(),
                    $this->context
                );
            }
        }

        return $ret;
    }

    /**
     * @return array<TaskGroup>
     */
    private function getTaskGroups(TourEntity $tourEntity): array
    {
        $taskGroups = [];

        foreach ($tourEntity->getTaskGroups() as $taskGroupEntity) {
            if (TaskGroupStatus::OBSOLETE !== $taskGroupEntity->getStatus()) {
                $taskGroup = new TaskGroup(
                    uuid: $taskGroupEntity->getId(),
                    name: $taskGroupEntity->getTitle(),
                    toBeDone: $taskGroupEntity->getToBeDone(),
                    status: $taskGroupEntity->getStatus(),
                    rules: array_map(Rule::fromValueObject(...), $taskGroupEntity->getRules()),
                    ruleLogic: $taskGroupEntity->getRuleLogic(),
                    ruleEffect: $taskGroupEntity->getRuleEffect(),
                    ruleDefault: $taskGroupEntity->getRuleDefault(),
                    additionalInformation: array_map(
                        AdditionalInformation::fromAdditionalInformationObject(...),
                        $taskGroupEntity->getAdditionalInformation(),
                    ),
                    trackable: $taskGroupEntity->isTrackable(),
                );

                $locationId = $taskGroupEntity->getLocation()?->getId();
                if (null !== $locationId) {
                    $this->locationIds[$locationId] = $locationId;
                    $this->locationsMap[$locationId]['taskgroup'][] = $taskGroupEntity->getId();
                }

                $this->taskGroups[$taskGroupEntity->getId()] = $taskGroup;
                $taskGroups[] = $taskGroup;
            }
        }

        return $taskGroups;
    }

    private function getTaskGroupTemplate(AccessibleTaskGroup $accessibleTaskGroup): TaskGroupTemplate
    {
        $taskGroupTemplate = new TaskGroupTemplate(
            templateUuid: $accessibleTaskGroup->getId(),
            name: $accessibleTaskGroup->getTitle(),
            toBeDone: $accessibleTaskGroup->getToBeDone(),
            rules: array_map(Rule::fromValueObject(...), $accessibleTaskGroup->getRules()),
            ruleLogic: $accessibleTaskGroup->getRuleLogic(),
            ruleEffect: $accessibleTaskGroup->getRuleEffect(),
            ruleDefault: $accessibleTaskGroup->getRuleDefault(),
            additionalInformation: array_map(
                AdditionalInformation::fromAdditionalInformationObject(...),
                $accessibleTaskGroup->getAdditionalInformation(),
            ),
            trackable: $accessibleTaskGroup->isTrackable(),
        );

        $locationId = $accessibleTaskGroup->getLocation()?->getId();
        if (null !== $locationId) {
            $this->locationIds[$locationId] = $locationId;
            $this->locationsMap[$locationId]['taskgroup-template'][] = $accessibleTaskGroup->getId();
        }

        $this->taskGroupTemplates[$accessibleTaskGroup->getId()] = $taskGroupTemplate;

        return $taskGroupTemplate;
    }

    private function getTask(TaskRelation $taskRelationEntity): Task
    {
        $taskEntity = $taskRelationEntity->getTask();
        $inputs = [];

        foreach ($taskEntity->getElements() as $element) {
            $inputs[] = Element::fromElementValueObject($element, $this->context);
        }

        return new Task(
            uuid: $taskEntity->getId(),
            name: $taskEntity->getName(),
            isRequired: false === $taskRelationEntity->isOptional(),
            rules: array_map(Rule::fromValueObject(...), $taskRelationEntity->getRules()),
            ruleLogic: $taskRelationEntity->getRuleLogic(),
            ruleEffect: $taskRelationEntity->getRuleEffect(),
            ruleDefault: $taskRelationEntity->getRuleDefault(),
            status: $taskEntity->getStatus(),
            inputs: $inputs,
        );
    }

    private function getOrderTask(TaskRelation $taskRelationEntity): OrderTask
    {
        $taskEntity = $taskRelationEntity->getTask();
        $inputs = [];

        foreach ($taskEntity->getElements() as $element) {
            $inputs[] = Element::fromElementValueObject($element, $this->context);
        }

        return new OrderTask(
            uuid: $taskEntity->getId(),
            name: $taskEntity->getName(),
            isRequired: false === $taskRelationEntity->isOptional(),
            repeatable: $taskEntity->isRepeatable(),
            rules: array_map(Rule::fromValueObject(...), $taskRelationEntity->getRules()),
            ruleLogic: $taskRelationEntity->getRuleLogic(),
            ruleEffect: $taskRelationEntity->getRuleEffect(),
            ruleDefault: $taskRelationEntity->getRuleDefault(),
            status: $taskEntity->getStatus(),
            repeatedFrom: null,
            inputs: $inputs,
        );
    }

    private function getTaskTemplate(AccessibleTaskRelation $accessibleTaskRelationEntity): TaskTemplate
    {
        $accessibleTask = $accessibleTaskRelationEntity->getAccessibleTask();
        $inputs = [];

        foreach ($accessibleTask->getElements() as $element) {
            $inputs[] = Element::fromElementValueObject($element, $this->context, isTemplate: true);
        }

        return new TaskTemplate(
            templateUuid: $accessibleTask->getId(),
            name: $accessibleTask->getName(),
            isRequired: false === $accessibleTaskRelationEntity->isOptional(),
            rules: array_map(Rule::fromValueObject(...), $accessibleTaskRelationEntity->getRules()),
            ruleLogic: $accessibleTaskRelationEntity->getRuleLogic(),
            ruleEffect: $accessibleTaskRelationEntity->getRuleEffect(),
            ruleDefault: $accessibleTaskRelationEntity->getRuleDefault(),
            inputs: $inputs,
        );
    }

    private function processTerminationTemplates(): void
    {
        $accessibleTaskGroupEntities = $this->accessibleTaskGroupRepository->findByAccessibleTerminations(
            array_keys($this->terminationTemplates)
        );

        foreach ($accessibleTaskGroupEntities as $accessibleTaskGroupEntity) {
            $accessibleTerminationId = $accessibleTaskGroupEntity->getAccessibleTermination()?->getId();
            assert(null !== $accessibleTerminationId);

            $this->terminationTemplates[$accessibleTerminationId]->taskGroups[] = $this->getTaskGroupTemplate($accessibleTaskGroupEntity);
        }
    }

    private function processTaskGroups(): void
    {
        $taskRelationEntities = $this->taskRelationRepository->findActiveByTaskGroups(array_keys($this->taskGroups)); // DB Query

        foreach ($taskRelationEntities as $taskRelationEntity) {
            $taskGroup = $this->taskGroups[$taskRelationEntity->getTaskGroup()->getId()];

            if ($taskGroup instanceof OrderTaskGroup) {
                $taskGroup->tasks[] = $this->getOrderTask($taskRelationEntity);
            } else {
                $taskGroup->tasks[] = $this->getTask($taskRelationEntity);
            }
        }
    }

    private function processTaskGroupTemplates(): void
    {
        $accessibleTaskRelationEntities = $this->accessibleTaskRelationRepository->findByAccessibleTaskGroups(array_keys($this->taskGroupTemplates)); // DB Query

        foreach ($accessibleTaskRelationEntities as $accessibleTaskRelationEntity) {
            $taskGroupTemplate = $this->taskGroupTemplates[$accessibleTaskRelationEntity->getAccessibleTaskGroup()->getId()];

            $taskGroupTemplate->tasks[] = $this->getTaskTemplate($accessibleTaskRelationEntity);
        }
    }

    private function processLocations(): void
    {
        $locations = $this->locationRepository->findBy(['id' => $this->locationIds]); // DB Query

        foreach ($locations as $location) {
            foreach ($this->locationsMap[$location->getId()] as $type => $typeIds) {
                switch ($type) {
                    case 'taskgroup':
                        foreach ($typeIds as $id) {
                            $this->taskGroups[$id]->additionalInformation = $this->context->getLocationFormatService()->prependLocationAsAdditionalInformationDtos(
                                locationDto: Location::fromLocationEntity($location),
                                additionalInformation: $this->taskGroups[$id]->additionalInformation,
                            );
                            $this->taskGroups[$id]->location = Location::fromLocationEntity($location);
                        }
                        break;
                    case 'taskgroup-template':
                        foreach ($typeIds as $id) {
                            $this->taskGroupTemplates[$id]->additionalInformation = $this->context->getLocationFormatService()->prependLocationAsAdditionalInformationDtos(
                                locationDto: Location::fromLocationEntity($location),
                                additionalInformation: $this->taskGroupTemplates[$id]->additionalInformation,
                            );
                            $this->taskGroupTemplates[$id]->location = Location::fromLocationEntity($location);
                        }
                        break;
                    case 'order':
                        foreach ($typeIds as $id) {
                            $this->orders[$id]->additionalInformation = $this->context->getLocationFormatService()->prependLocationAsAdditionalInformationDtos(
                                locationDto: Location::fromLocationEntity($location),
                                additionalInformation: $this->orders[$id]->additionalInformation ?? [],
                            );
                        }
                        break;
                }
            }
        }
    }

    private function reset(): void
    {
        $this->taskGroups = [];
        $this->taskGroupTemplates = [];
        $this->terminationTemplates = [];
        $this->locationIds = [];
        $this->locationsMap = [];
    }
}
