<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\EventSubscriber;

use App\Infrastructure\HermesAppApi\Exception\AppVersionException;
use App\Infrastructure\HermesAppApi\Resource\Dto\ApiErrorResponse;
use App\Infrastructure\HermesAppApi\Service\AppVersionNotifier;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Events\MetadataBuilt;
use PreZero\ApiBundle\Metadata\HeaderMetadata;
use PreZero\ApiBundle\Metadata\ResponseMetadata;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;

readonly class AppVersionListener
{
    public function __construct(
        #[Autowire('%env(APP_MINIMUM_VERSION)%')]
        private string $minimumVersion,
        #[Autowire('%env(APP_RECOMMENDED_VERSION)%')]
        private string $recommendedVersion,
        private LoggerInterface $logger,
        private AppVersionNotifier $appVersionNotifier,
    ) {
    }

    #[AsEventListener(priority: 2)]
    public function checkApiVersion(RequestEvent $event): void
    {
        $request = $event->getRequest();
        $version = $request->headers->get('app-version');

        if (null === $version || !$event->isMainRequest()) {
            return;
        }

        preg_match('/^(\d+\.\d+\.\d+).*/', $version, $matches);
        $semanticVersion = $matches[1] ?? null;

        if (null === $semanticVersion) {
            $this->logger->warning('Invalid app version', ['version' => $version]);

            return;
        }

        if (version_compare($version, $this->minimumVersion, '<')) {
            throw new AppVersionException($version, $this->minimumVersion);
        }

        if ($this->isCalledBeforePushConnection($request)) {
            return;
        }

        if (version_compare($version, $this->recommendedVersion, '<')) {
            $this->appVersionNotifier->notifyAppVersionLessThanRecommended($this->recommendedVersion);
        }
    }

    #[AsEventListener]
    public function addAppVersionToApiSpecification(MetadataBuilt $metadataBuilt): void
    {
        foreach ($metadataBuilt->resourceMetadata as $resourceMetadata) {
            if ('hermes_app' !== $resourceMetadata->area) {
                continue;
            }

            foreach ($resourceMetadata->operations as $operationMetadata) {
                $operationMetadata->headers = [
                    ...$operationMetadata->headers,
                    new HeaderMetadata(
                        name: 'app-version',
                        description: 'App version in semantic versioning format (e.g. 1.0.0)',
                        example: '1.0.0',
                    ),
                ];

                $operationMetadata->responses = [
                    ...$operationMetadata->responses,
                    new ResponseMetadata(
                        httpCode: 412,
                        description: 'App version is less than the minimum required version',
                        mimeType: 'application/json',
                        output: ApiErrorResponse::class,
                        contentType: ContentType::DTO,
                        normalizationContext: [],
                    ),
                ];
            }
        }
    }

    private function isCalledBeforePushConnection(Request $request): bool
    {
        return str_ends_with($request->getPathInfo(), '/notification/access');
    }
}
