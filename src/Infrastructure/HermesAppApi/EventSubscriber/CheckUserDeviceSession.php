<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\EventSubscriber;

use App\Domain\Context\DeviceContext;
use App\Domain\Security\Security;
use App\Domain\Services\ActiveUserDeviceStorage;
use App\Infrastructure\HermesAppApi\Exception\AuthException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;

#[AsEventListener]
readonly class CheckUserDeviceSession
{
    public function __construct(
        private ActiveUserDeviceStorage $activeUserDeviceStorage,
        private Security $security,
        private DeviceContext $deviceContext,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(RequestEvent $requestEvent): void
    {
        $request = $requestEvent->getRequest();

        if (!$this->isDeviceSessionRequired($request) || !$requestEvent->isMainRequest()) {
            return;
        }

        $userIdentifier = $this->security->getAuthenticatedUser()->getUserIdentifier();

        try {
            $currentActiveDeviceForUser = $this->activeUserDeviceStorage->getUserActiveDevice($userIdentifier);
        } catch (\RedisException $e) {
            $this->logger->error(
                'Failed to retrieve user active device from cache. Allowing request to pass through.',
                ['exception' => $e]
            );

            return;
        }

        if (
            // No active device for current user, he should not call the API
            null === $currentActiveDeviceForUser
            // If the active device for the user is different from the device making the request, reject the request
            || $currentActiveDeviceForUser !== $this->deviceContext->getDeviceId()
        ) {
            $this->logger->warning(
                'User session invalid, returning error',
                [
                    'userIdentifier' => $userIdentifier,
                    'currentActiveDeviceForUser' => $currentActiveDeviceForUser,
                    'requestDeviceId' => $this->deviceContext->getDeviceId(),
                ]
            );

            throw AuthException::sessionInvalid();
        }
    }

    private function isDeviceSessionRequired(Request $request): bool
    {
        $requestPath = $request->getPathInfo();

        return
            // Only check for user device session if the request is an API request
            str_starts_with($requestPath, '/api/v2')
            // Allow user session start request to pass through, so new session can be started even if old session is still active
            && !str_starts_with($requestPath, '/api/v2/user/start-session')
            // Other exclusions
            && !str_starts_with($requestPath, '/api/v2/device-access')
            && !str_starts_with($requestPath, '/api/v2/notification/access')
            && !str_starts_with($requestPath, '/api/v2/log/app')
            && !str_starts_with($requestPath, '/api/v2/log/navigation')
        ;
    }
}
