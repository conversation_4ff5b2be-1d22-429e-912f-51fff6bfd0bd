<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\EventSubscriber;

use Pre<PERSON>ero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Events\MetadataBuilt;
use PreZero\ApiBundle\Metadata\HeaderMetadata;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener]
readonly class AddActionHeaders
{
    public function __invoke(MetadataBuilt $metadataBuilt): void
    {
        foreach ($metadataBuilt->resourceMetadata as $resourceMetadata) {
            if ('hermes_app' !== $resourceMetadata->area) {
                continue;
            }

            foreach ($resourceMetadata->operations as $operationMetadata) {
                if (Get::class === $operationMetadata->type || GetCollection::class === $operationMetadata->type) {
                    continue;
                }

                $operationMetadata->headers = [
                    ...$operationMetadata->headers,
                    new HeaderMetadata(
                        name: 'x-action-timestamp',
                        format: 'date-time',
                        description: 'Device timestamp at the time the action occurred (might be different from the current timestamp if no network connection during action)',
                        required: true,
                        example: '2024-11-29T13:32:26.154+01:00'
                    ),
                    new HeaderMetadata(
                        name: 'x-action-latitude',
                        format: 'float',
                        description: 'Latitude of the device at the time of the action',
                        example: '51.848503',
                        type: 'number'
                    ),
                    new HeaderMetadata(
                        name: 'x-action-longitude',
                        format: 'float',
                        description: 'Longitude of the device at the time of the action',
                        example: '-0.554482',
                        type: 'number'
                    ),
                    new HeaderMetadata(
                        name: 'x-action-mileage',
                        format: 'float',
                        description: 'Mileage of the vehicle at the time of the action',
                        example: '123456',
                        type: 'number'
                    ),
                ];
            }
        }
    }
}
