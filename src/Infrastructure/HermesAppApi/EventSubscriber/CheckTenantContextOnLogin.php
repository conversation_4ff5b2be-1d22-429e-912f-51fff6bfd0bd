<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\EventSubscriber;

use App\Domain\Context\TenantContext;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;

#[AsEventListener]
readonly class CheckTenantContextOnLogin
{
    public function __construct(
        private LoggerInterface $logger,
        private TenantContext $tenantContext,
    ) {
    }

    public function __invoke(LoginSuccessEvent $event): void
    {
        $requestPath = $event->getRequest()->getPathInfo();
        $user = $event->getUser();

        if (!str_starts_with($requestPath, '/api/v2/')) {
            return;
        }

        try {
            $this->tenantContext->getTenant();
        } catch (\RuntimeException) {
            $this->logger->critical(
                'Cannot resolve tenant for authenticated user.',
                [
                    'username' => $user->getUserIdentifier(),
                    'class' => $user::class,
                    'requestPath' => $requestPath,
                ],
            );
            throw new UnauthorizedHttpException('can not login user with no tenant');
        }
    }
}
