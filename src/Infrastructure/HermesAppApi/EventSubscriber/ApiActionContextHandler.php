<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\EventSubscriber;

use App\Domain\Context\ActionContext;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

#[AsEventListener(priority: 1)]
readonly class ApiActionContextHandler
{
    public function __construct(
        private ActionContext $actionContext,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        if (!$this->isApiActionRequest($event) || !$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();

        $timestampHeader = $request->headers->get('x-action-timestamp');
        $latitudeHeader = $request->headers->get('x-action-latitude');
        $longitudeHeader = $request->headers->get('x-action-longitude');
        $mileageHeader = $request->headers->get('x-action-mileage');
        $requestTimeHeader = $request->headers->get('x-request-start');

        try {
            $timestamp = null !== $timestampHeader ? new \DateTimeImmutable($timestampHeader) : null;
        } catch (\DateMalformedStringException $e) {
            throw new BadRequestHttpException('API Actions should contain valid "x-action-timestamp" header', previous: $e);
        }

        $latitude = null !== $latitudeHeader ? (float) $latitudeHeader : null;
        $longitude = null !== $longitudeHeader ? (float) $longitudeHeader : null;
        $mileage = null !== $mileageHeader ? (float) $mileageHeader : null;

        if (null === $timestamp) {
            throw new BadRequestHttpException('API Actions should contain "x-action-timestamp" header');
        }

        $this->logger->debug(
            'Loading into action context from Hermes APP API request',
            [
                'timestamp' => $timestamp,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'mileage' => $mileage,
            ],
        );

        if (null !== $requestTimeHeader && preg_match('/^\d+\.\d+$/', $requestTimeHeader)) {
            $requestTime = \DateTimeImmutable::createFromTimestamp((float) $requestTimeHeader);
            $this->logger->debug(
                'Setting request time from header',
                ['requestTime' => $requestTime->format('Y-m-d H:i:s.u'), 'header' => $requestTimeHeader]
            );
            $this->actionContext->setRequestTime($requestTime);
        }

        $this->actionContext
            ->setUnadjustedTimestamp($timestamp)
            ->setLatitude($latitude)
            ->setLongitude($longitude)
            ->setMileage($mileage);
    }

    private function isApiActionRequest(RequestEvent $event): bool
    {
        $request = $event->getRequest();
        $urlPath = $request->getPathInfo();
        $actionMethods = [
            Request::METHOD_POST,
            Request::METHOD_PUT,
            Request::METHOD_PATCH,
        ];

        return str_starts_with($urlPath, '/api/v2')
            && in_array($request->getMethod(), $actionMethods, true);
    }
}
