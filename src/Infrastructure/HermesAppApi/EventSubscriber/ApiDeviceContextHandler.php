<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\EventSubscriber;

use App\Domain\Context\DeviceContext;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

#[AsEventListener(priority: 3)]
readonly class ApiDeviceContextHandler
{
    public function __construct(
        private DeviceContext $deviceContext,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        if (!$this->isApiRequest($event) || !$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();

        $timestampHeader = $request->headers->get('x-device-timestamp');
        $idHeader = $request->headers->get('x-device-id');

        try {
            $timestamp = null !== $timestampHeader ? new \DateTimeImmutable($timestampHeader) : null;
        } catch (\DateMalformedStringException $e) {
            throw new BadRequestHttpException('API Requests should contain valid "x-device-timestamp" header', previous: $e);
        }

        if (null === $timestamp) {
            throw new BadRequestHttpException('API Requests should contain "x-device-timestamp" header');
        }

        if (null === $idHeader) {
            throw new BadRequestHttpException('API Requests should contain "x-device-id" header');
        }

        $this->logger->debug(
            'Loading into device context from Hermes APP API request',
            [
                'timestamp' => $timestamp,
                'deviceId' => $idHeader,
            ],
        );

        $this->deviceContext
            ->setDeviceTimestamp($timestamp)
            ->setDeviceId($idHeader);
    }

    private function isApiRequest(RequestEvent $event): bool
    {
        return str_starts_with($event->getRequest()->getPathInfo(), '/api/v2');
    }
}
