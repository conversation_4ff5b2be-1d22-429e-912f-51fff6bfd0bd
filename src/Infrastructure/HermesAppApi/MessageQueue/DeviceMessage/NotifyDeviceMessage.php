<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\DeviceMessage;

use App\Domain\MessageQueue\AsyncMessage;
use App\Infrastructure\HermesAppApi\Notification\Dto\ActiveTourUpdated;

readonly class NotifyDeviceMessage implements AsyncMessage
{
    public function __construct(
        public string $deviceId,
        public ActiveTourUpdated $activeTourUpdated,
    ) {
    }
}
