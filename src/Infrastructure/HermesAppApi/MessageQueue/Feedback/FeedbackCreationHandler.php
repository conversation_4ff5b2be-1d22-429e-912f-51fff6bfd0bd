<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Feedback;

use App\Domain\Entity\Feedback;
use App\Domain\Repository\FeedbackRepository;
use App\Domain\Services\SessionService;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class FeedbackCreationHandler
{
    public function __construct(
        private SessionService $sessionService,
        private FeedbackRepository $feedbackRepository,
    ) {
    }

    public function __invoke(FeedbackCreationMessage $message): void
    {
        $session = $this->sessionService->getActiveDeviceSession();

        $feedback = new Feedback()
            ->setComment($message->feedback->comment ?? '')
            ->setLogFile($message->feedback->logFile)
            ->setScreenshotFile($message->feedback->screenshotFile)
            ->setSessionId($session->getId());

        $this->feedbackRepository->save($feedback);
    }
}
