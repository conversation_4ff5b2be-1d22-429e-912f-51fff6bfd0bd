<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Tour;

use App\Domain\MessageQueue\DeviceMessage;
use App\Domain\MessageQueue\RequireActionContext;
use App\Domain\MessageQueue\RequireUserIdentifier;
use Symfony\Component\Validator\Constraints as Assert;

readonly class TourEndMessage implements
    DeviceMessage,
    RequireUserIdentifier,
    RequireActionContext
{
    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Uuid]
        public string $tourId,

        public bool $force = false,
    ) {
    }
}
