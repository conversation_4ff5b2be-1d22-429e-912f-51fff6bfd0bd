<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Tour;

use App\Domain\Repository\TourRepository;
use App\Infrastructure\HermesAppApi\Event\TourStatusChangedFromDeviceAsyncEvent;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

#[AsMessageHandler]
readonly class TourStatusChangedHandler
{
    public function __construct(
        private TourRepository $tourRepository,
        private LoggerInterface $logger,
        private EventDispatcherInterface $eventDispatcher,
    ) {
    }

    public function __invoke(TourStatusChangedMessage $message): void
    {
        $tour = $this->tourRepository->find($message->tourId);

        if (null === $tour) {
            $this->logger->critical('Tour not found in TourStatusChangedHandler', ['tourId' => $message->tourId]);

            return;
        }

        $this->eventDispatcher->dispatch(new TourStatusChangedFromDeviceAsyncEvent($tour));
    }
}
