<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Tour;

use App\Domain\Entity\TaskGroup;
use App\Domain\Repository\TourRepository;
use App\Domain\Services\SessionService;
use App\Infrastructure\HermesAppApi\Service\TaskGroupService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class TourChangeTaskGroupHandler
{
    public function __construct(
        private TourRepository $tourRepository,
        private SessionService $sessionService,
        private TaskGroupService $taskGroupService,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(TourChangeTaskGroupMessage $message): void
    {
        $tour = $this->tourRepository->find($message->tourId);

        if (null === $tour) {
            $this->logger->critical(
                'taskgroup in tour could not be patched - order not found',
                [
                    'message' => $message,
                    'taskGroupId' => $message->taskGroup->uuid,
                    'tourId' => $message->tourId,
                ]
            );

            return;
        }

        $this->sessionService->getActiveDeviceSession();

        $taskGroupId = $message->taskGroup->uuid;

        $taskGroup = $tour->getTaskGroups()
            ->filter(fn (TaskGroup $taskGroup): bool => $taskGroup->getId() === $taskGroupId)
            ->first();

        if (!$taskGroup) {
            $this->logger->critical(
                'taskgroup in tour could not be patched - taskgroup  not found',
                [
                    'message' => $message,
                    'taskGroupId' => $taskGroupId,
                    'tourId' => $message->tourId,
                ]
            );

            return;
        }

        $this->taskGroupService->updateTaskGroup(
            taskGroup: $taskGroup,
            taskGroupUpdateData: $message->taskGroup,
        );
    }
}
