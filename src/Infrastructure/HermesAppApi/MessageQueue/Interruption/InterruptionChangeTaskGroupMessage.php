<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Interruption;

use App\Domain\MessageQueue\DeviceMessage;
use App\Domain\MessageQueue\RequireActionContext;
use App\Domain\MessageQueue\RequireUserIdentifier;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroupTemplate;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

readonly class InterruptionChangeTaskGroupMessage implements
    DeviceMessage,
    RequireUserIdentifier,
    RequireActionContext
{
    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Uuid]
        public string $clientId,

        #[Assert\Valid]
        #[SerializedName('taskgroup')]
        public TaskGroupTemplate $taskGroup,
    ) {
    }
}
