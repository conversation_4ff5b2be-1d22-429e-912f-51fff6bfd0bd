<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\User;

use App\Domain\Services\SessionService;
use App\Exception\BadRequestException;
use App\Infrastructure\Framework\Symfony\Context\RequestContextInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class UserEndSessionHandler
{
    public function __construct(
        private SessionService $sessionService,
        private RequestContextInterface $requestContext,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(UserEndSessionMessage $message): void
    {
        $user = $this->requestContext->getUser();

        try {
            if (null !== $user) {
                $this->sessionService->endUserSession($user);

                $this->logger->info(
                    'user end session async',
                );
            }
        } catch (BadRequestException $exception) {
            $this->logger->critical(
                'user end session throws an error',
                [
                    'exception' => $exception,
                ]
            );
        }
    }
}
