<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\User;

use App\Domain\Entity\SessionUser;
use App\Domain\Entity\User;
use App\Domain\Services\SessionService;
use App\Infrastructure\Framework\Symfony\Context\RequestContextInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class UserChangeProfileHandler
{
    public function __construct(
        private SessionService $sessionService,
        private RequestContextInterface $requestContext,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(UserChangeProfileMessage $message): void
    {
        $user = $this->requestContext->getUser();
        $deviceSession = $this->sessionService->getActiveDeviceSession();

        if (!$user instanceof User) {
            $this->logger->critical(
                'user-profile could not be patched - user not found',
                [
                    'message' => $message,
                ]
            );

            return;
        }

        $userInSessionCount = $deviceSession
            ->getSessionUsers()
            ->filter(fn (SessionUser $sessionUser): bool => $sessionUser->getUser() === $user)
            ->count();

        if (0 === $userInSessionCount) {
            $this->logger->critical(
                'user-profile could not be patched - user not in session',
                [
                    'message' => $message,
                ]
            );

            return;
        }

        $user->saveUserSettings(
            language: $message->user->language,
            colorMode: $message->user->colorMode,
            optionButtonLocation: $message->user->optionButtonLocation,
        );
    }
}
