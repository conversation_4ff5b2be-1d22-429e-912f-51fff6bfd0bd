<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Equipment;

use App\Domain\MessageQueue\DeviceMessage;
use App\Domain\MessageQueue\RequireActionContext;
use App\Domain\MessageQueue\RequireUserIdentifier;
use App\Infrastructure\HermesAppApi\Resource\Dto\Input\TaskGroup;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

readonly class EquipmentTaskGroupMessage implements
    DeviceMessage,
    RequireUserIdentifier,
    RequireActionContext
{
    public function __construct(
        public string $equipmentId,

        #[Assert\Valid]
        #[SerializedName('taskgroup')]
        public TaskGroup $taskGroup,
    ) {
    }
}
