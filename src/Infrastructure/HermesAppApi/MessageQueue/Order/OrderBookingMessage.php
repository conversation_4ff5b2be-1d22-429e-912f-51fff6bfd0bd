<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Order;

use App\Domain\MessageQueue\DeviceMessage;
use App\Domain\MessageQueue\RequireActionContext;
use App\Domain\MessageQueue\RequireUserIdentifier;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingRequest;

readonly class OrderBookingMessage implements
    DeviceMessage,
    RequireUserIdentifier,
    RequireActionContext
{
    public function __construct(
        public string $orderId,
        public BookingRequest $bookingRequest,
    ) {
    }
}
