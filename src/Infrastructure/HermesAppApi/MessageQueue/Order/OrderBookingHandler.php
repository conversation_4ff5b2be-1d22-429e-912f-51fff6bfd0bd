<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Order;

use App\Domain\Repository\OrderRepository;
use App\Domain\Services\SessionService;
use App\Domain\Workflow\Workflow;
use App\Exception\BadRequestException;
use App\Infrastructure\HermesAppApi\Resource\Dto\Booking\BookingValue;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class OrderBookingHandler
{
    public function __construct(
        private OrderRepository $orderRepository,
        private SessionService $sessionService,
        private LoggerInterface $logger,
        private Workflow $workflow,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function __invoke(OrderBookingMessage $message): void
    {
        $this->sessionService->getActiveDeviceSession();

        $order = $this->orderRepository->find($message->orderId);

        if (null === $order) {
            $this->logger->critical(
                'Order could not be booked - no order was found',
                [
                    'orderId' => $message->orderId,
                    'booking' => $message->bookingRequest->booking,
                ],
            );

            return;
        }

        $this->workflow->applyForOrder(
            order: $order,
            transition: match ($message->bookingRequest->booking) {
                BookingValue::START => 'start',
                BookingValue::END => 'end',
                BookingValue::TERMINATION => 'terminate',
            },
        );
        $this->logger->info(
            'order booked',
            [
                'orderId' => $order->getId(),
                'booking' => $message->bookingRequest->booking,
            ]
        );
    }
}
