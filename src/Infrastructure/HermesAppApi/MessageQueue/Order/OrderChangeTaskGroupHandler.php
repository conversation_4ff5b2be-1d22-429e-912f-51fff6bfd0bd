<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Order;

use App\Domain\Entity\TaskGroup;
use App\Domain\Repository\OrderRepository;
use App\Domain\Services\SessionService;
use App\Infrastructure\HermesAppApi\Service\TaskGroupService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class OrderChangeTaskGroupHandler
{
    public function __construct(
        private OrderRepository $orderRepository,
        private SessionService $sessionService,
        private TaskGroupService $taskGroupService,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(OrderChangeTaskGroupMessage $message): void
    {
        $order = $this->orderRepository->find($message->orderId);

        if (null === $order) {
            $this->logger->critical(
                'taskgroup in order could not be patched - order not found',
                [
                    'message' => $message,
                    'taskGroupId' => $message->taskGroup->uuid,
                    'orderId' => $message->orderId,
                ]
            );

            return;
        }

        $this->sessionService->getActiveDeviceSession();

        $taskGroupId = $message->taskGroup->uuid;

        $taskGroup = $order->getTaskGroups()
            ->filter(fn (TaskGroup $taskGroup): bool => $taskGroup->getId() === $taskGroupId)
            ->first();

        if (!$taskGroup) {
            $this->logger->critical(
                'taskgroup in order could not be patched - taskgroup  not found',
                [
                    'message' => $message,
                    'taskGroupId' => $taskGroupId,
                    'orderId' => $message->orderId,
                ]
            );

            return;
        }

        $this->taskGroupService->updateTaskGroup(
            taskGroup: $taskGroup,
            taskGroupUpdateData: $message->taskGroup,
        );
    }
}
