<?php

declare(strict_types=1);

namespace App\Infrastructure\HermesAppApi\MessageQueue\Order;

use App\Domain\Entity\AccessibleNote;
use App\Domain\Entity\AccessibleNoteRelation;
use App\Domain\Entity\Enum\Status\OrderStatus;
use App\Domain\Repository\OrderRepository;
use App\Domain\Services\SessionService;
use App\Exception\BadRequestException;
use App\Infrastructure\HermesAppApi\Service\TemplateObjectService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class OrderNoteHandler
{
    public function __construct(
        private OrderRepository $orderRepository,
        private SessionService $sessionService,
        private EntityManagerInterface $manager,
        private TemplateObjectService $templateObjectService,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function __invoke(OrderNoteMessage $message): void
    {
        $order = $this->orderRepository->find($message->orderId);

        if (null === $order) {
            $this->logger->critical(
                'order-note could not be stored - order not found',
                [
                    'message' => $message,
                    'orderId' => $message->orderId,
                ]
            );

            return;
        }

        $this->sessionService->getActiveDeviceSession();

        /** @var AccessibleNote|false $noteTemplate */
        $noteTemplate = $order->getAccessibleNoteRelations()
            ->map(fn (AccessibleNoteRelation $relation): AccessibleNote => $relation->getAccessibleNote())
            ->filter(fn (AccessibleNote $note): bool => $note->getId() === $message->note->templateUuid)
            ->first();

        if (!$noteTemplate) {
            $this->logger->critical(
                'Order-note could not be stored - template not found',
                [
                    'orderId' => $message->orderId,
                    'message' => $message,
                    'templateId' => $message->note->templateUuid,
                ],
            );

            return;
        }

        $note = $noteTemplate->createNote();
        $order->addNote($note);

        foreach ($message->note->taskGroups as $taskGroup) {
            $this->templateObjectService->addTaskGroupFromTemplate($note, $noteTemplate, $taskGroup);
        }

        if (OrderStatus::OBSOLETE === $order->getStatus()) {
            $this->logger->info(
                'note-set on obsolete order',
                [
                    'message' => $message,
                    'order' => $message->orderId,
                    'templateId' => $message->note->templateUuid,
                ]
            );
        }

        $this->manager->persist($order);
    }
}
