<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Stamp;

use Symfony\Component\Messenger\Stamp\StampInterface;
use Symfony\Component\Serializer\Annotation\Context;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;

readonly class DeviceMetadataStamp implements StampInterface
{
    public function __construct(
        #[Context(
            context: [DateTimeNormalizer::FORMAT_KEY => \DateTimeInterface::RFC3339_EXTENDED],
        )]
        public \DateTimeImmutable $deviceTimestamp,

        public ?string $deviceId = null,
    ) {
    }
}
