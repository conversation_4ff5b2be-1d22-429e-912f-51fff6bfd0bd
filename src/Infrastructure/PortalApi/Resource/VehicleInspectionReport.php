<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\VehicleInspection\Report\Outcome;
use App\Infrastructure\PortalApi\HttpEndpoint\VehicleInspectionEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\VehicleInspection\EquipmentComponentState;
use App\Infrastructure\PortalApi\Resource\Dto\VehicleInspection\VehicleInspectionConfig;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [VehicleInspectionEndpoint::class, 'list'],
            uriTemplate: '/vehicle-inspection-report/{equipmentId}', pathParameters: [
                new PathParameter(
                    name: 'equipmentId',
                    type: 'string',
                    description: 'The ID of the equipment for which the inspection reports are requested.',
                    constraint: Requirement::UUID,
                ),
            ],
            filters: [
                new Filter(
                    parameterName: 'date',
                    parameterDescription: 'Date in format yyyy-mm-dd (e.g. 2024-09-23)',
                    parameterFormat: 'date',
                    validators: [new Date()]
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_LIST]],
            responseOpenApiSchemaName: 'VehicleInspectionReportListResponse',
        ),
        new Get(
            controller: [VehicleInspectionEndpoint::class, 'getReport'],
            uriTemplate: '/vehicle-inspection-report/{equipmentId}/report/{reportId}', pathParameters: [
                new PathParameter(
                    name: 'equipmentId',
                    type: 'string',
                    description: 'The ID of the equipment for which the inspection report is requested.',
                    constraint: Requirement::UUID,
                ),
                new PathParameter(
                    name: 'reportId',
                    type: 'string',
                    description: 'The ID of the vehicle inspection report to retrieve.',
                ),
            ],
            output: Dto\VehicleInspection\VehicleInspectionReport::class,
        ),
        new Get(
            controller: [VehicleInspectionEndpoint::class, 'template'],
            uriTemplate: '/vehicle-inspection-report/{equipmentId}/template', pathParameters: [
                new PathParameter(
                    name: 'equipmentId',
                    type: 'string',
                    description: 'The ID of the equipment for which the inspection report template is requested.',
                    constraint: Requirement::UUID,
                ),
            ],
            output: VehicleInspectionConfig::class,
        ),
        new Post(
            controller: [VehicleInspectionEndpoint::class, 'submit'],
            uriTemplate: '/vehicle-inspection-report/{equipmentId}', pathParameters: [
                new PathParameter(
                    name: 'equipmentId',
                    type: 'string',
                    description: 'The ID of the equipment for which the inspection report is submitted.',
                    constraint: Requirement::UUID,
                ),
            ],
            denormalizationContext: ['groups' => [self::GROUP_SUBMIT]],
            requestOpenApiSchemaName: 'VehicleInspectionReportSubmission',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: null,
    tag: 'Vehicle Inspection Report',
)]
class VehicleInspectionReport
{
    public const string GROUP_LIST = 'vehicle-inspection:list';
    public const string GROUP_SUBMIT = 'vehicle-inspection:submit';

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('report_id')]
    public string $reportId;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('reported_at')]
    public \DateTimeImmutable $reportedAt;

    #[Groups([self::GROUP_LIST])]
    public Outcome $outcome;

    #[Groups([self::GROUP_LIST])]
    public string $label;

    /**
     * @var array<EquipmentComponentState>
     */
    #[Groups([self::GROUP_SUBMIT])]
    #[SerializedName('components_state')]
    public array $componentsState;

    #[Assert\Length(max: 65535)]
    #[Groups([self::GROUP_SUBMIT])]
    #[SerializedName('description')]
    public ?string $description = null;

    /**
     * @var array<string>
     */
    #[Groups([self::GROUP_SUBMIT])]
    #[SerializedName('uploaded_files')]
    public array $uploadedFiles = [];
}
