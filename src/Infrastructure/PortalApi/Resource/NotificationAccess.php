<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Infrastructure\PortalApi\HttpEndpoint\NotificationEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [NotificationEndpoint::class, 'getAccess'],
            summary: 'Get notification system access data.',
            uriTemplate: '/notification/access',
        ),
    ],
    identifier: null,
    tag: 'Notification',
)]
class NotificationAccess
{
    #[SerializedName('access_token')]
    public string $accessToken;

    public string $endpoint;
}
