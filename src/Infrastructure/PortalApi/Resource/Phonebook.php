<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\PhonebookEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\NotBlank;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [PhonebookEndpoint::class, 'getCollection'],
            denormalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'phonebookListItem',
        ),
        new Get(
            controller: [PhonebookEndpoint::class, 'get'],

            pathParameters: [
                new PathParameter(
                    name: 'id',

                    type: 'string',

                    description: 'Id',

                    constraint: Requirement::UUID,
                ),
            ],
            denormalizationContext: ['groups' => [self::GROUP_ITEM]],
            responseOpenApiSchemaName: 'phonebookItem',
        ),
        new Post(
            controller: [PhonebookEndpoint::class, 'create'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_PHONEBOOKS->value.'")',
            denormalizationContext: ['groups' => [self::GROUP_UPDATE_CREATE]],
            requestOpenApiSchemaName: 'phonebookCreateItem',
            responseOpenApiSchemaName: 'phonebookItem',
        ),
        new Patch(
            controller: [PhonebookEndpoint::class, 'patch'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_PHONEBOOKS->value.'")', denormalizationContext: ['groups' => [self::GROUP_UPDATE_CREATE]],
            requestOpenApiSchemaName: 'phonebookUpsertItem',
            responseOpenApiSchemaName: 'phonebookItem',
        ),
        new Delete(
            controller: [PhonebookEndpoint::class, 'delete'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_PHONEBOOKS->value.'")', ),
    ],
    identifier: 'id',
    tag: 'Phonebook',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_PHONEBOOKS->value.'")',
)]
class Phonebook
{
    private const string GROUP_COLLECTION = 'phonebook:collection';
    private const string GROUP_ITEM = 'phonebook:item';
    private const string GROUP_UPDATE_CREATE = 'phonebook:update-create';

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    public string $id;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_UPDATE_CREATE])]
    #[NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public string $name;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_UPDATE_CREATE])]
    public ?string $description;

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_UPDATE_CREATE])]
    #[Assert\All([
        new Assert\Uuid(),
    ])]
    #[NotBlank]
    public array $branches = [];

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    #[SerializedName('branch_external_ids')]
    public array $branchExternalIds = [];

    /**
     * @var Contact[]
     */
    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    public array $contacts = [];
}
