<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\BranchEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\FilterType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [BranchEndpoint::class, 'getCollection'],
            security: '
                is_granted("'.UserRole::ROLE_PORTAL_MANAGE_PORTAL_USERS->value.'")
                or is_granted("'.UserRole::ROLE_PORTAL_MANAGE_COUNTRY_ADMINS->value.'")
                or is_granted("'.UserRole::ROLE_PORTAL_MANAGE_SUPPORT_USERS->value.'")
                or is_granted("'.UserRole::ROLE_PORTAL_MANAGE_FAQ_ADMINS->value.'")
            ',
            uriTemplate: '/branch/{country}',
            pathParameters: [
                new PathParameter(
                    name: 'country',
                    type: 'string',
                    description: 'The country of the branch',
                    constraint: '[a-z]{2,3}',
                ),
            ],
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'BranchListItem',
        ),
        new GetCollection(
            controller: [BranchEndpoint::class, 'getCollectionAccessible'],
            security: '
                is_granted("'.UserRole::ROLE_PORTAL_ACCESS->value.'")
            ',
            uriTemplate: '/branch-accessible',
            filters: [
                new Filter(
                    parameterName: 'externalId',
                    filterType: FilterType::STRING_PARTIAL,
                    parameterDescription: 'Filter for the external-id of the branch',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Length(max: 50),
                    ],
                ),
            ],
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'BranchListItem',
        ),
    ],
    identifier: 'branchId',
    tag: 'Branch',
)]
class Branch
{
    #[SerializedName('branch_id')]
    public string $branchId;

    #[SerializedName('external_id')]
    public string $externalId;
}
