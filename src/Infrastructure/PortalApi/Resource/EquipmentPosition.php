<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\Status\EquipmentStatus;
use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\EquipmentPositionEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [EquipmentPositionEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'branchId',
                    parameterDescription: 'Filter for the id of the branch.',
                    parameterFormat: 'string',
                    multiple: true,
                ),
                new Filter(
                    parameterName: 'equipmentType',
                    parameterDescription: 'Filter for the type of the equipment.',
                    parameterFormat: 'string',
                    multiple: true,
                ),
                new Filter(
                    parameterName: 'licensePlate',
                    parameterDescription: 'Filter for the license plate of the equipment (case insensitive).',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'externalId',
                    parameterDescription: 'Filter for the external id of the equipment (case insensitive).',
                    parameterFormat: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'EquipmentPositionList',
        ),
    ],
    identifier: 'equipmentId',
    tag: 'Equipment',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT->value.'")',
)]
class EquipmentPosition
{
    #[SerializedName('equipment_id')]
    public string $equipmentId;

    #[SerializedName('external_id')]
    public string $externalId;

    public EquipmentType $type;

    #[SerializedName('license_plate')]
    public string $licensePlate;

    public EquipmentStatus $status;

    #[SerializedName('status_label')]
    public string $statusLabel;

    public ?float $latitude;

    public ?float $longitude;

    #[SerializedName('last_position_timestamp')]
    public ?\DateTimeImmutable $lastPositionTimestamp;
}
