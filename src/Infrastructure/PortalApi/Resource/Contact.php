<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\ContactEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\NotBlank;

#[ApiResource(
    area: 'portal',
    operations: [
        new Post(
            controller: [ContactEndpoint::class, 'create'],
            uriTemplate: '/phonebook/{phonebookId}/contact', pathParameters: [
                new PathParameter(name: 'phonebookId', description: 'ID of the contacts phonebook'),
            ],
            requestOpenApiSchemaName: 'contactCreateItem',
        ),
        new Patch(
            controller: [ContactEndpoint::class, 'update'],
            uriTemplate: '/phonebook/{phonebookId}/contact', pathParameters: [
                new PathParameter(name: 'phonebookId', description: 'ID of the contacts phonebook'),
            ],
            requestOpenApiSchemaName: 'contactUpsertItem',
        ),
        new Delete(
            controller: [ContactEndpoint::class, 'delete'],
            uriTemplate: '/phonebook/{phonebookId}/contact/{contactId}', pathParameters: [
                new PathParameter(name: 'phonebookId', description: 'ID of the contacts phonebook'),
                new PathParameter(name: 'contactId', description: 'ID of the contact to delete'),
            ],
        ),
    ],
    identifier: 'id',
    tag: 'Phonebook',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_PHONEBOOKS->value.'")',
)]
class Contact
{
    public ?string $id = null;

    #[NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    #[SerializedName('first_name')]
    public string $firstName;

    #[NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    #[SerializedName('last_name')]
    public string $lastName;

    #[NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    #[SerializedName('country_prefix')]
    public string $countryPrefix;

    #[NotBlank]
    #[SerializedName('phone_number')]
    public int $phoneNumber;

    #[SerializedName('emergency_contact')]
    public bool $emergencyContact;

    public ?string $description;
}
