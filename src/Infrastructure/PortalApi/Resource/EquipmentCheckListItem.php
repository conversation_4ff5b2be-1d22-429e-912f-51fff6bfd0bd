<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\EquipmentCheckEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\FilterType;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [EquipmentCheckEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'equipmentId',
                    filterType: FilterType::STRING_EXACT,
                    parameterDescription: 'Filter for the equipment id',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'from',
                    parameterDescription: 'Filter for start-date minimum.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'to',
                    parameterDescription: 'Filter for start-date maximum.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
        ),
    ],
    name: 'equipment-check',
    identifier: null,
    tag: 'Equipment',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT_CHECKS->value.'")',
)]
class EquipmentCheckListItem
{
    #[SerializedName('session_equipment_id')]
    public string $sessionEquipmentId;

    #[SerializedName('equipment_id')]
    public string $equipmentId;

    #[SerializedName('external_id')]
    public string $externalId;

    public EquipmentType $type;

    #[SerializedName('license_plate')]
    public string $licensePlate;

    public ?\DateTimeInterface $start;

    #[SerializedName('count_positive')]
    public int $countPositive;

    #[SerializedName('count_negative')]
    public int $countNegative;

    #[SerializedName('count_not_answered')]
    public int $countNotAnswered;
}
