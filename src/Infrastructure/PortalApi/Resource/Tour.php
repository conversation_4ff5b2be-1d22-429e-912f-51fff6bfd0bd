<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Infrastructure\PortalApi\HttpEndpoint\TourEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\Output\TourOutput;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [TourEndpoint::class, 'details'],
            responseDescription: 'Details of tour',
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
            output: TourOutput::class,
            responseOpenApiSchemaName: 'TourDetailsItem'
        ),
        new GetCollection(
            controller: [TourEndpoint::class, 'list'],
            uriTemplate: '/tour',
            filters: [
                new Filter(
                    parameterName: 'date',
                    parameterDescription: 'Date for equipment in format yyyy-mm-dd (e.g. 2024-09-23)',
                    parameterFormat: 'date',
                    validators: [new Date()]
                ),
                new Filter(
                    parameterName: 'externalId',
                    parameterDescription: 'Filter for external_id',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_LIST]],
            output: TourOutput::class,
            responseOpenApiSchemaName: 'TourListItem',
        ),
        new GetCollection(
            controller: [TourEndpoint::class, 'listByDate'],
            uriTemplate: '/tour/list-by-date/{date}', pathParameters: [
                new PathParameter('date', 'string', 'Date in format yyyy-mm-dd (e.g. 2021-12-31)'),
            ],
            queryParameters: [
                new QueryParameter(
                    name: 'hasOrderFiles',
                    type: 'string',
                    description: 'Only show tours with order-files (default true)',
                    enumValues: ['1', '0'],
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_LIST_BY_DATE]],
            responseOpenApiSchemaName: 'TourSelectorListItem',
        ),
        new Patch(
            controller: [TourEndpoint::class, 'setMastertours'],
            uriTemplate: '/tour/{tourId}/mastertours', pathParameters: [
                new PathParameter('tourId', 'string', 'ID of the Tour'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            requestOpenApiSchemaName: 'TourUpdateMastertours',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: 'tourId',
    tag: 'Tour',
)]
class Tour
{
    private const string GROUP_LIST = 'tour:list';
    private const string GROUP_DETAILS = 'tour:details';
    private const string GROUP_UPDATE = 'tour:update';
    private const string GROUP_LIST_BY_DATE = 'tour:list-by-date';

    #[Groups([self::GROUP_LIST_BY_DATE])]
    #[SerializedName('tour_id')]
    public string $tourId;

    /** @var array<string> */
    #[Groups([self::GROUP_UPDATE])]
    #[SerializedName('mastertours')]
    public ?array $mastertours = null;

    #[Groups([self::GROUP_LIST_BY_DATE])]
    #[SerializedName('selector_label')]
    public ?string $selectorLabel = null;
}
