<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\MastertourProgressEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\MastertourProgressEquipmentAction;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [MastertourProgressEndpoint::class, 'getListPerDay'],
            uriTemplate: '/mastertour-progress/{mastertourTemplateId}/{date}', pathParameters: [
                new PathParameter(
                    name: 'mastertourTemplateId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
                new PathParameter(
                    name: 'date',
                    type: 'string',
                    description: 'Date in format yyyy-mm-dd (e.g. 2021-12-31)',
                ),
            ],
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'MastertourProgressListItem',
        ),
        new GetCollection(
            controller: [MastertourProgressEndpoint::class, 'list'],
            uriTemplate: '/mastertour-progress/{mastertourTemplateId}', pathParameters: [
                new PathParameter(
                    name: 'mastertourTemplateId',
                    type: 'string',
                    constraint: Requirement::UUID,
                ),
            ],
            responseOpenApiSchemaName: 'MastertourProgressListItem'
        ),
    ],
    identifier: 'mastertourTemplateId',
    tag: 'MastertourProgress',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_MASTERTOUR_PROGESS->value.'")'
)]
class MastertourProgress
{
    #[SerializedName('mastertour_template_id')]
    public string $mastertourTemplateId;

    #[SerializedName('waypoint_id')]
    public string $waypointId;

    public \DateTimeImmutable $date;

    /**
     * @var array<MastertourProgressEquipmentAction>
     */
    #[SerializedName('equipment_actions')]
    public array $equipmentActions = [];

    public float $latitude;

    public float $longitude;
}
