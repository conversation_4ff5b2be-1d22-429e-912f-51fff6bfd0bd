<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\DeviceMessageThreadEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\DeviceMessageTarget;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [DeviceMessageThreadEndpoint::class, 'list'],
            responseOpenApiSchemaName: 'DeviceMessageThreadItem',
        ),
        new GetCollection(
            controller: [DeviceMessageThreadEndpoint::class, 'search'],
            name: 'portal-device-message-thread-search',
            uriTemplate: '/device-message-thread/search/{query}',
            pathParameters: [
                new PathParameter(
                    name: 'query',
                    description: 'Searches in staff\'s first name, last name and external ID or equipment\'s external ID and license plate.',
                ),
            ],
            responseOpenApiSchemaName: 'DeviceMessageThreadListItem',
        ),
    ],
    identifier: 'id',
    tag: 'Device Messages',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_DEVICE_MESSAGE_ACCESS->value.'")',
)]
class DeviceMessageThread
{
    public string $id;

    #[SerializedName('has_unread_messages')]
    public bool $hasUnreadMessages;

    #[SerializedName('updated_at')]
    public \DateTimeImmutable $updatedAt;

    #[SerializedName('last_message_excerpt')]
    public string $lastMessageExcerpt;

    public DeviceMessageTarget $target;

    #[SerializedName('target_identifier')]
    public string $targetIdentifier;
}
