<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\DashboardEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use <PERSON><PERSON><PERSON>\OpenApi\Schema\Type;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Annotation\Context;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Validator\Constraints\Date;
use Vuryss\Serializer\Attribute\SerializerContext;
use Vuryss\Serializer\SerializerInterface;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [DashboardEndpoint::class, 'get'],
            uriTemplate: '/dashboard',
            filters: [
                new Filter(
                    parameterName: 'date',
                    parameterDescription: 'Date in format yyyy-mm-dd (e.g. 2021-12-31)',
                    parameterFormat: 'date',
                    validators: [new Date()]
                ),
            ]
        ),
    ],
    identifier: null,
    tag: 'Dashboard',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_DASHBOARD->value.'")',
)]
class Dashboard
{
    /**
     * @var DashboardTour[]
     */
    public array $tours;

    #[SerializedName('completed_tours')]
    public int $completedTours = 0;

    #[SerializedName('active_tours')]
    public int $activeTours = 0;

    #[SerializedName('created_tours')]
    public int $createdTours = 0;

    #[SerializedName('terminated_tours')]
    public int $terminatedTours = 0;

    #[SerializedName('active_equipments')]
    public int $activeEquipments = 0;

    #[SerializedName('selected_date')]
    #[SerializerContext(attributes: [SerializerInterface::ATTRIBUTE_DATETIME_FORMAT => 'Y-m-d'])]
    #[Context(context: [DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
    #[Property(schema: new Schema(type: Type::STRING, format: 'date'))]
    public \DateTimeInterface $selectedDate;
}
