<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\EquipmentTypeEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\EquipmentTypeList;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [EquipmentTypeEndpoint::class, 'getEquipmentTypes'],
            name: 'portal-equipment-types',
            uriTemplate: '/equipment-types',
            responseDescription: 'List of Equipment types',
            output: EquipmentTypeList::class,
            responseOpenApiSchemaName: 'EquipmentTypeListItem',
        ),
    ],
    identifier: null,
    tag: 'Equipment',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_DEVICE_MESSAGE_ACCESS->value.'")',
)]
class EquipmentType
{
}
