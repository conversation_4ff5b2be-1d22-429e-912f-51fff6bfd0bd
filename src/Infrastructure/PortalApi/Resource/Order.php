<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\OrderEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [OrderEndpoint::class, 'listByTour'],
            uriTemplate: '/tour/{tourId}/order', pathParameters: [
                new PathParameter(
                    name: 'tourId',
                    type: 'string',
                    description: 'Tour ID',
                    constraint: Requirement::UUID,
                ),
            ],
            queryParameters: [
                new QueryParameter(
                    name: 'hasFiles',
                    type: 'string',
                    description: 'Only show order with files (default true)',
                    enumValues: ['1', '0'],
                ),
            ],
            pagination: Pagination::NONE,
        ),
    ],
    identifier: 'orderId',
    tag: 'Order',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_TOURS->value.'")',
)]
class Order
{
    #[SerializedName('order_id')]
    public string $orderId;

    #[SerializedName('external_id')]
    public string $externalId;

    public int $position;
}
