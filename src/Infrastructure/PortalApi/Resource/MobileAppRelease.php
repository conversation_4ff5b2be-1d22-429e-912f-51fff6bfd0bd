<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\MobileAppReleaseEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\ExternalFileDownloadDto;
use App\Infrastructure\PortalApi\Resource\Dto\Output\MobileAppReleaseFile;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [MobileAppReleaseEndpoint::class, 'list'],
            filters: [
                new Filter(
                    parameterName: 'date',
                    parameterDescription: 'Filter for the date of the release in format yyyy-mm-dd (e.g. 2025-07-17)',
                    parameterFormat: 'date',
                    validators: [new Date()]
                ),
                new Filter(
                    parameterName: 'type',
                    parameterDescription: 'Filter for the type of the app (hermes_app, container_app, maintenance_app).',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Length(min: 1, max: 20),
                    ],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_LIST]],
        ),
        new Get(
            controller: [MobileAppReleaseEndpoint::class, 'details'],
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
        ),
        new Get(
            controller: [MobileAppReleaseEndpoint::class, 'download'],
            uriTemplate: '/mobile-app-release/{releaseId}/download/{fileId}', pathParameters: [
                new PathParameter(
                    name: 'releaseId',
                    type: 'string',
                    description: 'The ID of the release.',
                ),
                new PathParameter(
                    name: 'fileId',
                    type: 'string',
                    description: 'The ID of the file.',
                ),
            ],
            responseType: ContentType::DTO,
            output: ExternalFileDownloadDto::class,
        ),
    ],
    tag: 'Mobile APP Releases',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_MOBILE_APP_RELEASES->value.'")',
)]
class MobileAppRelease
{
    private const string GROUP_LIST = 'mobile-app-release:list';
    private const string GROUP_DETAILS = 'mobile-app-release:details';

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    public string $id;

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    public \DateTimeImmutable $date;

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    public string $version;

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    public string $type;

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    #[SerializedName('status_badges')]
    public array $statusBadges = [];

    #[Groups([self::GROUP_DETAILS])]
    public string $releaseNotes;

    /**
     * @var MobileAppReleaseFile[]
     */
    #[Groups([self::GROUP_DETAILS])]
    public array $files = [];
}
