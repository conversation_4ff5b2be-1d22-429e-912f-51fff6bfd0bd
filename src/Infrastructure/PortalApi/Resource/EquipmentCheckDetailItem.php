<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\EquipmentCheckEndpoint;
use App\Infrastructure\PortalApi\Resource\Common\TaskGroup;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [EquipmentCheckEndpoint::class, 'get'],
        ),
    ],
    name: 'equipment-check',
    identifier: 'sessionEquipmentId',
    tag: 'Equipment',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT_CHECKS->value.'")',
)]
class EquipmentCheckDetailItem
{
    #[SerializedName('session_equipment_id')]
    public string $sessionEquipmentId;

    #[SerializedName('equipment_id')]
    public string $equipmentId;

    #[SerializedName('external_id')]
    public string $externalId;

    public EquipmentType $type;

    #[SerializedName('license_plate')]
    public string $licensePlate;

    public ?\DateTimeInterface $start;

    /**
     * @var array<TaskGroup>
     */
    #[SerializedName('task_groups')]
    public array $taskGroups = [];
}
