<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Infrastructure\PortalApi\HttpEndpoint\CountryEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\CountryList;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [CountryEndpoint::class, 'get'],
            responseDescription: 'List of country codes',
            output: CountryList::class,
            responseOpenApiSchemaName: 'CountryListItem'
        ),
    ],
    identifier: null,
    tag: 'Country',
)]
class Country
{
}
