<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\Country;
use App\Domain\Entity\Enum\Locale;
use App\Domain\Entity\Enum\UserGroup;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\Exception\TranslatableHttpException;
use App\Infrastructure\PortalApi\HttpEndpoint\UserEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [UserEndpoint::class, 'getCollection'],
            queryParameters: [
                new QueryParameter(
                    name: 'query',
                    type: 'string',
                    description: 'Search query'
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_LIST]],
            responseOpenApiSchemaName: 'UserListItem',
        ),
        new Get(
            controller: [UserEndpoint::class, 'getProfile'],
            name: 'portal-user-profile',
            security: 'is_granted("'.UserRole::ROLE_PORTAL_ACCESS->value.'")',
            uriTemplate: '/user/me',
            pathParameters: [],
            normalizationContext: ['groups' => [self::GROUP_PROFILE]],
            responseOpenApiSchemaName: 'UserProfileItem',
        ),
        new Get(
            controller: [UserEndpoint::class, 'details'],

            pathParameters: [
                new PathParameter(
                    name: 'userId',

                    type: 'string',

                    description: 'UserId',

                    constraint: Requirement::UUID,
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
            responseOpenApiSchemaName: 'UserDetailsItem',
        ),
        new Get(
            controller: [UserEndpoint::class, 'checkUsername'],
            name: 'portal-user-username-check',
            uriTemplate: '/user/check-username/{username}',
            pathParameters: [
                new PathParameter(
                    name: 'username',
                    type: 'string',
                    description: 'Username to check',
                    constraint: '[a-zA-Z0-9_\-\.@+%]+',
                ),
            ],
            responses: [
                new Response(httpCode: 200, description: 'Username is available'),
                new Response(httpCode: 409, description: 'Username is already taken', contentType: ContentType::EMPTY),
            ],
        ),
        new Get(
            controller: [UserEndpoint::class, 'checkEmail'],
            name: 'portal-user-email-check',
            uriTemplate: '/user/check-email/{email}',
            pathParameters: [
                new PathParameter(
                    name: 'email',
                    type: 'string',
                    description: 'Email to check',
                    constraint: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                ),
            ],
            responses: [
                new Response(httpCode: 200, description: 'Email is available'),
                new Response(httpCode: 409, description: 'Email is already taken', contentType: ContentType::EMPTY),
            ],
        ),
        new Post(
            controller: [UserEndpoint::class, 'create'],
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'UserCreationItem',
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
            responseOpenApiSchemaName: 'UserDetailsItem',
        ),
        new Patch(
            controller: [UserEndpoint::class, 'updateProfile'],
            name: 'portal-user-profile-update',
            security: 'is_granted("'.UserRole::ROLE_PORTAL_CHANGE_PROFILE->value.'")',
            uriTemplate: '/user/me',
            pathParameters: [],
            denormalizationContext: ['groups' => [self::GROUP_PROFILE_UPDATE]],
            requestOpenApiSchemaName: 'UserProfileUpdateItem',
            normalizationContext: ['groups' => [self::GROUP_PROFILE]],
            responseOpenApiSchemaName: 'UserProfileItem',
        ),
        new Patch(
            controller: [UserEndpoint::class, 'update'],

            pathParameters: [
                new PathParameter(
                    name: 'userId',

                    type: 'string',

                    description: 'UserId',

                    constraint: Requirement::UUID,
                ),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            requestOpenApiSchemaName: 'UserUpdateItem',
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
            responseOpenApiSchemaName: 'UserDetailsItem',
        ),
    ],
    identifier: 'userId',
    tag: 'User',
    security: '
        is_granted("'.UserRole::ROLE_PORTAL_MANAGE_PORTAL_USERS->value.'")
        or is_granted("'.UserRole::ROLE_PORTAL_MANAGE_COUNTRY_ADMINS->value.'")
        or is_granted("'.UserRole::ROLE_PORTAL_MANAGE_SUPPORT_USERS->value.'")
        or is_granted("'.UserRole::ROLE_PORTAL_MANAGE_FAQ_ADMINS->value.'")
    ',
)]
class User
{
    private const string GROUP_PROFILE = 'user:profile';
    private const string GROUP_PROFILE_UPDATE = 'user:profile:update';
    private const string GROUP_LIST = 'user:list';
    private const string GROUP_DETAILS = 'user:details';
    private const string GROUP_CREATE = 'user:create';
    private const string GROUP_UPDATE = 'user:update';

    #[Groups([self::GROUP_DETAILS, self::GROUP_LIST, self::GROUP_PROFILE])]
    #[SerializedName('user_id')]
    #[Assert\Uuid]
    public string $userId;

    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_LIST, self::GROUP_PROFILE])]
    #[SerializedName('username')]
    #[Assert\Length(min: 1, max: 255)]
    public string $userName;

    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_LIST, self::GROUP_PROFILE, self::GROUP_UPDATE])]
    #[SerializedName('first_name')]
    #[Assert\Length(min: 1, max: 255)]
    #[Assert\Regex('/^[^<>&\"\v$%!#?§;*~\/\\|\^=\[\]{}\(\)]+$/')]
    public string $firstName;

    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_LIST, self::GROUP_PROFILE, self::GROUP_UPDATE])]
    #[SerializedName('last_name')]
    #[Assert\Length(min: 1, max: 255)]
    #[Assert\Regex('/^[^<>&\"\v$%!#?§;*~\/\\|\^=\[\]{}\(\)]+$/')]
    public string $lastName;

    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_LIST, self::GROUP_PROFILE, self::GROUP_UPDATE])]
    #[Assert\Length(min: 1, max: 255)]
    #[Assert\Email]
    public string $email;

    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_UPDATE, self::GROUP_LIST])]
    public ?Country $country;

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_PROFILE])]
    public array $roles;

    /**
     * @var UserGroup[]
     */
    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_PROFILE, self::GROUP_UPDATE])]
    public array $groups;

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_UPDATE])]
    #[SerializedName('branch_access')]
    #[Assert\All([
        new Assert\Uuid(),
    ])]
    public array $branchAccess = [];

    /**
     * @var Country[]
     */
    #[Groups([self::GROUP_CREATE, self::GROUP_DETAILS, self::GROUP_UPDATE])]
    #[SerializedName('country_access')]
    public array $countryAccess = [];

    /**
     * @var UserGroup[]
     */
    #[Groups([self::GROUP_PROFILE])]
    #[SerializedName('groups_user_can_delegate')]
    public array $groupsUserCanDelegate;

    #[Groups([self::GROUP_PROFILE])]
    #[SerializedName('last_update')]
    public ?\DateTimeInterface $modifiedAt;

    #[Groups([self::GROUP_PROFILE_UPDATE, self::GROUP_PROFILE])]
    public Locale $language;

    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('change_group_permission')]
    public bool $changeGroupPermission;

    public function validateCreateUserFieldsExistence(): void
    {
        if (!isset($this->userName)) {
            throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Username is required', 'user/error/username-required');
        }

        $this->validateUpdateUserFieldsExistence();
    }

    public function validateUpdateUserFieldsExistence(): void
    {
        if (!isset($this->firstName)) {
            throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'First name is required', 'user/error/firstname-required');
        }

        if (!isset($this->lastName)) {
            throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Last name is required', 'user/error/lastname-required');
        }

        if (!isset($this->country)) {
            throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Country is required', 'user/error/country-required');
        }

        if (!isset($this->groups)) {
            throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Groups are required', 'user/error/groups-required');
        }

        if (!isset($this->email)) {
            throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Email is required', 'user/error/email-required');
        }

        if (in_array(UserGroup::COUNTRY_ADMIN, $this->groups, true) || in_array(UserGroup::PORTAL_ADMIN, $this->groups, true)) {
            if ([] === $this->countryAccess) {
                throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Country access is required for country/portal admin', 'user/error/country-access-required');
            }

            if ([] !== $this->branchAccess) {
                throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Branch access is not allowed for country/portal admin', 'user/error/branch-access-not-allowed');
            }
        } else {
            if ([] === $this->branchAccess) {
                throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Branch access is required', 'user/error/branch-access-required');
            }

            if ([] !== $this->countryAccess) {
                throw new TranslatableHttpException(HttpResponse::HTTP_UNPROCESSABLE_ENTITY, 'Country access is not allowed for branch user', 'user/error/country-access-not-allowed');
            }
        }
    }
}
