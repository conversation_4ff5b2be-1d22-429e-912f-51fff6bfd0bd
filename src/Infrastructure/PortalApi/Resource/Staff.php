<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\Status\StaffStatus;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\StaffEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\Staff\DebugModeInput;
use App\Infrastructure\PortalApi\Resource\Dto\Staff\DeviceAccessInput;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [StaffEndpoint::class, 'get'],
            pathParameters: [
                new PathParameter(
                    name: 'staffId',
                    type: 'string',
                    description: 'Staff ID',
                    constraint: Requirement::UUID,
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
            responseOpenApiSchemaName: 'StaffDetailsItem',
        ),
        new GetCollection(
            controller: [StaffEndpoint::class, 'getCollection'],
            queryParameters: [
                new QueryParameter(
                    name: 'query',
                    type: 'string',
                    description: 'Search query'
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_LIST]],
            responseOpenApiSchemaName: 'StaffListItem',
        ),
        new GetCollection(
            controller: [StaffEndpoint::class, 'getStaffListByBranch'],
            name: 'portal-staff-list-by-branch',
            security: 'is_granted("'.UserRole::ROLE_PORTAL_DEVICE_MESSAGE_ACCESS->value.'")',
            uriTemplate: '/staff-list',
            filters: [
                new Filter(
                    parameterName: 'branchId',
                    required: true,
                    parameterDescription: 'Filter for the id of the branch.',
                    parameterFormat: 'string',
                    validators: [new Assert\Uuid()]
                ),
            ],
            responseOpenApiSchemaName: 'StaffBranchListItem',
        ),
        new Post(
            controller: [StaffEndpoint::class, 'resetPassword'],
            uriTemplate: '/staff/{staffId}/password',
            pathParameters: [
                new PathParameter(
                    name: 'staffId',
                    type: 'string',
                    description: 'Staff Id',
                    constraint: Requirement::UUID,
                ),
            ],
            requestType: ContentType::EMPTY,
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [StaffEndpoint::class, 'giveDeviceAccess'],
            name: 'portal-staff-give-device-access',
            summary: 'Gives special device access to a staff user.',
            security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_DEVICE_ACCESS->value.'")',
            uriTemplate: '/staff/{staffId}/device-access',
            pathParameters: [
                new PathParameter(
                    name: 'staffId',
                    type: 'string',
                    description: 'ID of the Staff',
                    constraint: Requirement::UUID,
                ),
            ],
            input: DeviceAccessInput::class,
            requestOpenApiSchemaName: 'DeviceAccessCreationItem',
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [StaffEndpoint::class, 'setDeviceDebugMode'],
            name: 'portal-staff-set-device-debug-mode',
            summary: 'set extended device debug mode.',
            security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_DEVICE_ACCESS->value.'")',
            uriTemplate: '/staff/{staffId}/device-debug-mode',
            pathParameters: [
                new PathParameter(
                    name: 'staffId',
                    type: 'string',
                    description: 'ID of the Staff',
                    constraint: Requirement::UUID,
                ),
            ],
            input: DebugModeInput::class,
            requestOpenApiSchemaName: 'DeviceDebugModeItem',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: 'staffId',
    tag: 'Staff',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_STAFF->value.'")',
)]
class Staff
{
    public const string GROUP_DETAILS = 'staff:details';
    public const string GROUP_LIST = 'staff:list';

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    #[SerializedName('staff_id')]
    public string $staffId;

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    #[SerializedName('external_id')]
    public string $externalId;

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    #[SerializedName('personnel_type')]
    public string $personnelType;

    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('branch_id')]
    public string $branchId;

    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('branch_external_id')]
    public string $branchExternalId;

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    public string $firstname;

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    public string $lastname;

    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('last_tour')]
    public ?string $lastTour;

    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('last_device')]
    public ?string $lastDevice;

    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('last_session')]
    public ?string $lastSession;

    #[Groups([self::GROUP_DETAILS])]
    public ?string $icon = null;

    #[Groups([self::GROUP_DETAILS])]
    public StaffStatus $status;

    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('status_label')]
    public string $statusLabel;
}
