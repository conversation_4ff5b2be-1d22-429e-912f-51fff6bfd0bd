<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\EquipmentPushTrackingEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\EquipmentPushTrackingResponse;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new Post(
            controller: [EquipmentPushTrackingEndpoint::class, 'push'],
            uriTemplate: '/equipment/push-tracking',
            output: EquipmentPushTrackingResponse::class,
            successHttpCode: 200,
        ),
    ],

    identifier: null,
    tag: 'Equipment',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT->value.'")',
)]
class EquipmentPushTracking
{
    /** @var array<string> */
    #[Assert\All([
        new Assert\Type(type: 'string'),
        new Assert\Uuid(),
    ])]
    public array $equipments = [];
}
