<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\FaqController;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Regex;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [FaqController::class, 'getCollection'],
            responseOpenApiSchemaName: 'FaqItem'
        ),
        new Get(
            controller: [FaqController::class, 'get'],
            responseOpenApiSchemaName: 'FaqItem'
        ),
        new Post(
            controller: [FaqController::class, 'create'],
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'FaqCreationItem',
            responseOpenApiSchemaName: 'FaqItem',
        ),
        new Patch(
            controller: [FaqController::class, 'update'],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            requestOpenApiSchemaName: 'FaqUpdateItem',
            responseOpenApiSchemaName: 'FaqItem'
        ),
        new Delete(
            controller: [FaqController::class, 'delete'],
        ),
    ],
    identifier: 'faqId',
    tag: 'Faq',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_FAQ->value.'")',
)]
class Faq
{
    private const string GROUP_CREATE = 'faq:create';
    private const string GROUP_UPDATE = 'faq:update';

    #[SerializedName('faq_id')]
    public string $faqId;

    #[Groups([self::GROUP_CREATE, self::GROUP_UPDATE])]
    #[Assert\NotBlank]
    public string $question;

    #[Groups([self::GROUP_CREATE, self::GROUP_UPDATE])]
    #[Assert\NotBlank]
    public string $answer;

    #[Groups([self::GROUP_CREATE, self::GROUP_UPDATE])]
    #[Assert\NotBlank]
    #[Assert\LessThan(2147483648)]
    public int $sequence;

    #[Groups(self::GROUP_CREATE)]
    #[Regex(pattern: '/^[a-z]{2}_[A-Z]{2}$/', message: 'Invalid ISO code')]
    #[Assert\NotBlank(groups: [self::GROUP_CREATE])]
    public string $iso;
}
