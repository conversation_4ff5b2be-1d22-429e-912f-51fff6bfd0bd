<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\FileEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [FileEndpoint::class, 'listInOrder'],
            uriTemplate: '/order/{orderId}/file',
            pathParameters: [
                new PathParameter(
                    name: 'orderId',
                    type: 'string',
                    description: 'ID of the order',
                    constraint: Requirement::UUID,
                ),
            ],
            pagination: Pagination::NONE,
        ),
        new GetCollection(
            controller: [FileEndpoint::class, 'listInMultipleOrders'],
            uriTemplate: '/order/file',
            queryParameters: [
                new QueryParameter(
                    name: 'orderId',
                    type: 'string',
                    format: Requirement::UUID,
                    description: 'IDs of the orders to search',
                    required: true,
                    multipleValues: true,
                ),
            ],
            pagination: Pagination::NONE,
        ),
        new Get(
            controller: [FileEndpoint::class, 'get'],
            pathParameters: [
                new PathParameter(
                    name: 'filePath',
                    type: 'string',
                    description: 'File path',
                    constraint: '((pz|pzl|pzn|pze)\/)?(device-message|api-json|backend-documents|app-user-files|app-documents|vehicle-inspection)\/[a-zA-Z0-9_\-\.]+',
                ),
            ],
            requestDescription: 'Binary file contents',
            responseType: ContentType::BINARY,
        ),
        new Put(
            controller: [FileEndpoint::class, 'put'],
            uriTemplate: '/file/{directory}/{id}',
            pathParameters: [
                new PathParameter(
                    name: 'directory',
                    type: 'string',
                    description: 'Available options: device-message, vehicle-inspection',
                    constraint: 'device-message|vehicle-inspection',
                ),
                new PathParameter(
                    name: 'id',
                    type: 'string',
                    description: 'Portal-generated UUID for the file',
                    constraint: Requirement::UUID,
                ),
            ],
            requestType: ContentType::BINARY,
            requestDescription: 'Binary file contents',
            responseDescription: 'File uploaded',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: 'filePath',
    tag: 'File',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_FILES->value.'")',
)]
class File
{
    #[SerializedName('file_path')]
    public string $filePath;

    public \DateTimeImmutable $date;

    public string $name;

    public string $origin;

    #[SerializedName('file_name')]
    public string $fileName;

    #[SerializedName('order_position')]
    public int $orderPosition;
}
