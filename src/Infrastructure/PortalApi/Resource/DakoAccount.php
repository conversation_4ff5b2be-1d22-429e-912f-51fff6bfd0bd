<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\DakoAcccountEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new Post(
            controller: [DakoAcccountEndpoint::class, 'create'],
            name: 'portal-dako-account-create',
            description: 'Create a new account for dako',
            denormalizationContext: ['groups' => [self::GROUP_UPDATE_CREATE]],
            requestOpenApiSchemaName: 'DakoAccountUpsertItem',
            responseOpenApiSchemaName: 'DakoAccountUpsertResponseItem',
        ),
        new GetCollection(
            controller: [DakoAcccountEndpoint::class, 'getCollection'],
            name: 'portal-dako-account-get-collection',
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'DakoAccountListItem',
        ),
        new Get(
            controller: [DakoAcccountEndpoint::class, 'get'],
            name: 'portal-dako-account-get-details',
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
            responseOpenApiSchemaName: 'DakoAccountDetailItem',
        ),
        new Patch(
            controller: [DakoAcccountEndpoint::class, 'update'],
            name: 'portal-dako-account-update',
            denormalizationContext: ['groups' => [self::GROUP_UPDATE_CREATE]],
            requestOpenApiSchemaName: 'DakoAccountUpsertItem',
            responseOpenApiSchemaName: 'DakoAccountUpsertResponseItem',
        ),
    ],
    identifier: 'dakoAccountId',
    tag: 'Dako',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_DAKO_ACCOUNTS->value.'")',
)]
class DakoAccount
{
    private const string GROUP_UPDATE_CREATE = 'dako-account:update-create';
    private const string GROUP_COLLECTION = 'dako-account:collection';
    private const string GROUP_DETAILS = 'dako-account:details';

    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAILS])]
    #[SerializedName('dako_account_id')]
    public string $dakoAccountId;

    #[Groups([self::GROUP_UPDATE_CREATE, self::GROUP_COLLECTION, self::GROUP_DETAILS])]
    #[Assert\NotBlank]
    public string $name;

    #[SerializedName('dako_id')]
    #[Groups([self::GROUP_UPDATE_CREATE, self::GROUP_COLLECTION, self::GROUP_DETAILS])]
    #[Assert\NotBlank]
    public string $dakoId;

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_UPDATE_CREATE, self::GROUP_DETAILS])]
    #[Assert\All([
        new Assert\Uuid(),
    ])]
    public array $branches = [];
}
