<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Infrastructure\PortalApi\HttpEndpoint\TranslationEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\Output\TranslationIsoOutput;
use App\Infrastructure\PortalApi\Resource\Dto\Output\TranslationOutput;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [TranslationEndpoint::class, 'getIsos'],
            uriTemplate: '/translation/locale',
            pagination: Pagination::NONE,
            output: TranslationIsoOutput::class
        ),
        new GetCollection(
            controller: [TranslationEndpoint::class, 'getTranslations'],
            uriTemplate: '/translation/locale/{iso}',
            pathParameters: [
                new PathParameter(
                    name: 'iso',
                    type: 'string',
                    description: 'ISO locale code',
                    constraint: '[a-z]{2}_[A-Z]{2}',
                ),
            ],
            pagination: Pagination::NONE,
            output: TranslationOutput::class,
        ),
    ],
    identifier: null,
    tag: 'Translation',
)]
class Translation
{
}
