<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\EquipmentCondition;
use App\Domain\Entity\Enum\Status\EquipmentStatus;
use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\EquipmentEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\ConnectedDevice;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [EquipmentEndpoint::class, 'get'],
            normalizationContext: ['groups' => [self::GROUP_ITEM]],
            responseOpenApiSchemaName: 'EquipmentDetailsItem',
        ),
        new GetCollection(
            controller: [EquipmentEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'branchId',
                    parameterDescription: 'Filter for the id of the branch.',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Uuid(),
                    ],
                    multiple: true,
                ),
                new Filter(
                    parameterName: 'equipmentType',
                    parameterDescription: 'Filter for the type of the equipment.',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Length(min: 1, max: 20),
                    ],
                    multiple: true,
                ),
                new Filter(
                    parameterName: 'licensePlate',
                    parameterDescription: 'Filter for the license plate of the equipment (case insensitive).',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Length(min: 1, max: 255),
                    ],
                ),
                new Filter(
                    parameterName: 'externalId',
                    parameterDescription: 'Filter for the external id of the equipment (case insensitive).',
                    parameterFormat: 'string',
                    validators: [
                        new Assert\NotBlank(),
                        new Assert\Length(min: 1, max: 50),
                    ],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'EquipmentListItem',
        ),
        new GetCollection(
            controller: [EquipmentEndpoint::class, 'search'],
            name: 'portal-equipment-search',
            uriTemplate: '/equipment/search/{query}', pathParameters: [new PathParameter(name: 'query', description: 'Search query')],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_SEARCH]],
            responseOpenApiSchemaName: 'EquipmentSearchResultItem',
        ),
        new Post(
            controller: [EquipmentEndpoint::class, 'create'],
            name: 'portal-equipment-create',
            description: 'Create a new equipment',
            denormalizationContext: ['groups' => [self::GROUP_CREATE_UPDATE]],
            requestOpenApiSchemaName: 'EquipmentUpsertItem',
            responseType: ContentType::EMPTY,
        ),
        new Post(
            controller: [EquipmentEndpoint::class, 'stopEquipmentSessions'],
            name: 'stop-equipment-sessions',
            description: 'Stop all sessions that are connected to an active session-equipment of the given equipment',
            uriTemplate: '/equipment/stop-sessions/{equipmentId}', pathParameters: [
                new PathParameter(
                    name: 'equipmentId',
                    type: 'string',
                    description: 'Equipment ID',
                    constraint: Requirement::UUID,
                ),
            ],
            requestType: ContentType::EMPTY,
            responseType: ContentType::EMPTY,
        ),
        new Patch(
            controller: [EquipmentEndpoint::class, 'update'],
            name: 'portal-equipment-update',
            description: 'Update an existing equipment',
            denormalizationContext: ['groups' => [self::GROUP_CREATE_UPDATE]],
            requestOpenApiSchemaName: 'EquipmentUpsertItem',
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
        new Delete(
            controller: [EquipmentEndpoint::class, 'delete'],
        ),
    ],
    identifier: 'equipmentId',
    tag: 'Equipment',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT->value.'")',
)]
class Equipment
{
    public const string GROUP_COLLECTION = 'equipment:collection';
    public const string GROUP_ITEM = 'equipment:item';
    public const string GROUP_SEARCH = 'equipment:search';
    public const string GROUP_CREATE_UPDATE = 'equipment:create';

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_SEARCH])]
    #[SerializedName('equipment_id')]
    public string $equipmentId;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_SEARCH, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('external_id')]
    public string $externalId;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_SEARCH, self::GROUP_CREATE_UPDATE])]
    public EquipmentType $type;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('branch_id')]
    #[Assert\NotBlank]
    #[Assert\Uuid]
    public string $branchId;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    public ?int $height;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    public ?int $length;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    public ?int $width;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    public ?int $weight;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('minimum_load')]
    public ?int $minimumLoad;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    public ?int $overload;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('load_capacity')]
    public ?int $loadCapacity;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('total_permissible_weight')]
    public ?int $totalPermissibleWeight;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('max_axle_load')]
    public ?int $maxAxleLoad;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_SEARCH, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('license_plate')]
    public string $licensePlate;

    #[Groups([self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('container_mounting')]
    public ?string $containerMounting;

    /**
     * @var array<ConnectedDevice>
     */
    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM, self::GROUP_CREATE_UPDATE])]
    #[SerializedName('connected_devices')]
    public array $connectedDevices = [];

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    #[SerializedName('active_tour')]
    public ?string $activeTour;

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('last_device')]
    public ?string $lastDevice;

    #[Groups([self::GROUP_ITEM])]
    #[SerializedName('last_session')]
    public ?string $lastSession;

    /**
     * @var array<EquipmentStaff>
     */
    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    #[SerializedName('last_staff')]
    public array $lastStaff = [];

    #[Groups([self::GROUP_ITEM])]
    public ?string $icon = null;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    public EquipmentStatus $status;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    #[SerializedName('status_label')]
    public string $statusLabel;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    public ?float $latitude;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    public ?float $longitude;

    #[Groups([self::GROUP_COLLECTION, self::GROUP_ITEM])]
    public EquipmentCondition $condition;

    #[Groups([self::GROUP_COLLECTION])]
    #[SerializedName('last_position_timestamp')]
    public ?\DateTimeImmutable $lastPositionTimestamp;
}
