<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\PointOfInterestEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\NotBlank;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [PointOfInterestEndpoint::class, 'getCollection'],
            responseOpenApiSchemaName: 'pointOfInterestItem',
        ),
        new Post(
            controller: [PointOfInterestEndpoint::class, 'create'],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE_CREATE]],
            requestOpenApiSchemaName: 'pointOfInterestUpsertItem',
            responseOpenApiSchemaName: 'pointOfInterestItem',
        ),
        new Patch(
            controller: [PointOfInterestEndpoint::class, 'update'],

            pathParameters: [
                new PathParameter(
                    name: 'poiId',

                    type: 'string',

                    description: 'PoiId',

                    constraint: Requirement::UUID,
                ),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE_CREATE]],
            requestOpenApiSchemaName: 'pointOfInterestUpsertItem',
            responseOpenApiSchemaName: 'pointOfInterestItem',
        ),
        new Delete(
            controller: [PointOfInterestEndpoint::class, 'delete'],

            pathParameters: [
                new PathParameter(
                    name: 'poiId',

                    type: 'string',

                    description: 'PoiId',

                    constraint: Requirement::UUID,
                ),
            ],
        ),
    ],
    identifier: 'poiId',
    tag: 'PointOfInterest',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_POI->value.'")',
)]
class PointOfInterest
{
    private const string GROUP_UPDATE_CREATE = 'poi:update-create';

    #[SerializedName('poi_id')]
    public string $poiId;

    #[Groups([self::GROUP_UPDATE_CREATE])]
    #[NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public string $name;

    #[Groups([self::GROUP_UPDATE_CREATE])]
    #[NotBlank]
    public float $longitude;

    #[Groups([self::GROUP_UPDATE_CREATE])]
    #[NotBlank]
    public float $latitude;

    #[Groups([self::GROUP_UPDATE_CREATE])]
    #[NotBlank]
    #[Assert\Length(min: 1, max: 50)]
    public string $icon;
}
