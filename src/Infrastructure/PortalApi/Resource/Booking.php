<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\Types\BookingType;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\BookingEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [BookingEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'tourId',
                    required: true,
                    parameterDescription: 'The tour which contains the bookings.',
                    parameterFormat: 'string',
                    validators: [new Assert\Uuid()]
                ),
                new Filter(
                    parameterName: 'sessionId',
                    required: true,
                    parameterDescription: 'Device Session ID which contains the bookings.',
                    parameterFormat: 'string',
                    validators: [new Assert\Uuid()]
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'BookingListItem'
        ),
    ],
    identifier: 'bookingId',
    tag: 'Booking',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT->value.'")',
)]
class Booking
{
    public const string GROUP_COLLECTION = 'booking:collection';

    #[Groups([self::GROUP_COLLECTION])]
    #[SerializedName('booking_id')]
    public string $bookingId;

    #[Groups([self::GROUP_COLLECTION])]
    public ?string $label;

    #[Groups([self::GROUP_COLLECTION])]
    public ?BookingType $type;

    #[Groups([self::GROUP_COLLECTION])]
    public ?float $longitude;

    #[Groups([self::GROUP_COLLECTION])]
    public ?float $latitude;

    #[Groups([self::GROUP_COLLECTION])]
    public \DateTimeImmutable $timestamp;

    #[Groups([self::GROUP_COLLECTION])]
    public ?string $description;
}
