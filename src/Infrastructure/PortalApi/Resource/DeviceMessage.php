<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\DeviceMessageFrom;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\DeviceMessageEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\DeviceMessage\MessageRecipientInterface;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [DeviceMessageEndpoint::class, 'list'],
            uriTemplate: '/device-message/{threadId}', pathParameters: [
                new PathParameter(
                    name: 'threadId',
                    description: 'ID of the thread from which the messages will be taken',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_GET]],
            responseOpenApiSchemaName: 'DeviceMessageListItem'
        ),
        new Post(
            controller: [DeviceMessageEndpoint::class, 'create'],
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'DeviceMessageCreationItem',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: 'id',
    tag: 'Device Messages',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_DEVICE_MESSAGE_ACCESS->value.'")',
)]
class DeviceMessage
{
    public const string GROUP_GET = 'device-message:get';
    public const string GROUP_CREATE = 'device-message:create';

    #[Groups([self::GROUP_GET])]
    public string $id;

    #[Groups([self::GROUP_GET])]
    public DeviceMessageFrom $from;

    #[Groups([self::GROUP_GET])]
    public string $username;

    #[Groups([self::GROUP_CREATE])]
    #[Assert\NotBlank]
    public MessageRecipientInterface $recipient;

    #[Groups([self::GROUP_GET, self::GROUP_CREATE])]
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 500)]
    public string $message;

    /** @var array<string> */
    #[Groups([self::GROUP_GET, self::GROUP_CREATE])]
    #[Assert\All([
        new Assert\NotBlank(),
        new Assert\Length(max: 255),
    ])]
    public array $attachments = [];

    #[SerializedName('created_at')]
    #[Groups([self::GROUP_GET])]
    public \DateTimeImmutable $createdAt;
}
