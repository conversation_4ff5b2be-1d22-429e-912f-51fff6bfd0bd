<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\MastertourTemplateEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\MastertourTemplateDto;
use App\Infrastructure\PortalApi\Resource\Dto\Output\TranslationOutput;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\FilterType;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [MastertourTemplateEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'filter',
                    filterType: FilterType::STRING_PARTIAL,
                    parameterDescription: 'Partial match for name or external id',
                    validators: [new Assert\NotBlank()]
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_LIST]],
            output: MastertourTemplateDto::class,
            responseOpenApiSchemaName: 'MastertourTemplateListItem',
        ),
        new GetCollection(
            controller: [MastertourTemplateEndpoint::class, 'getWaypointTranslations'],
            uriTemplate: '/mastertour-template/waypoint-translations/{iso}',
            pathParameters: [
                new PathParameter(
                    name: 'iso',
                    type: 'string',
                    description: 'ISO locale code',
                    constraint: '[a-z]{2}_[A-Z]{2}',
                ),
            ],
            pagination: Pagination::NONE,
            output: TranslationOutput::class,
        ),
        new Get(
            controller: [MastertourTemplateEndpoint::class, 'get'],

            pathParameters: [
                new PathParameter(
                    name: 'mastertourTemplateId',

                    type: 'string',

                    description: 'MastertourTemplateId',

                    constraint: Requirement::UUID,
                ),
            ],
            output: MastertourTemplateDto::class,
            responseOpenApiSchemaName: 'MastertourTemplateItem',
        ),
        new Post(
            controller: [MastertourTemplateEndpoint::class, 'create'],
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            input: MastertourTemplateDto::class,
            requestOpenApiSchemaName: 'MastertourCreationItem',
            output: MastertourTemplateDto::class,
            responseOpenApiSchemaName: 'MastertourTemplateItem',
        ),
        new Patch(
            controller: [MastertourTemplateEndpoint::class, 'update'],

            pathParameters: [
                new PathParameter(
                    name: 'mastertourTemplateId',

                    type: 'string',

                    description: 'MastertourTemplateId',

                    constraint: Requirement::UUID,
                ),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            input: MastertourTemplateDto::class,
            requestOpenApiSchemaName: 'MastertourUpdateItem',
            output: MastertourTemplateDto::class,
            responseOpenApiSchemaName: 'MastertourTemplateItem',
        ),
        new Delete(
            controller: [MastertourTemplateEndpoint::class, 'delete'],

            pathParameters: [
                new PathParameter(
                    name: 'mastertourTemplateId',

                    type: 'string',

                    description: 'MastertourTemplateId',

                    constraint: Requirement::UUID,
                ),
            ],
        ),
    ],
    identifier: 'mastertourTemplateId',
    tag: 'MastertourTemplate',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_MASTERTOURS->value.'")',
)]
class MastertourTemplate
{
    private const string GROUP_CREATE = 'mastertour-template:create';
    private const string GROUP_UPDATE = 'mastertour-template:update';
    private const string GROUP_LIST = 'mastertour-template:list';

    public string $mastertourTemplateId;
}
