<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Domain\Entity\Tracking as TrackingEntity;
use App\Domain\Entity\ValueObject\TrackLocation;
use App\Infrastructure\PortalApi\HttpEndpoint\TrackingEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [TrackingEndpoint::class, 'getTrackingList'],
            uriTemplate: '/tracking',
            filters: [
                new Filter(
                    parameterName: 'min_date',
                    parameterDescription: 'Date minimum in format yyyy-mm-dd (e.g. 2024-09-23)',
                    parameterFormat: 'date',
                    validators: [new Date()]
                ),
                new Filter(
                    parameterName: 'max_date',
                    parameterDescription: 'Date maximum in format yyyy-mm-dd (e.g. 2024-09-23)',
                    parameterFormat: 'date',
                    validators: [new Date()]
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'search in tourExternalId, equipmentExternalId, licensePlate',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_LIST]],
            responseOpenApiSchemaName: 'TrackingListItem',
        ),

        new Get(
            controller: [TrackingEndpoint::class, 'getTrackingDetails'],

            pathParameters: [
                new PathParameter(
                    name: 'trackingId',

                    type: 'string',

                    description: 'TrackingId',

                    constraint: Requirement::UUID,
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAILS]],
            responseOpenApiSchemaName: 'TrackingDetailsItem',
        ),
    ],
    identifier: 'trackingId',
    tag: 'Tracking',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT->value.'")',
)]
class Tracking
{
    private const string GROUP_LIST = 'tracking:list';
    private const string GROUP_DETAILS = 'tracking:details';

    #[Groups([self::GROUP_LIST, self::GROUP_DETAILS])]
    #[SerializedName('tracking_id')]
    public string $trackingId;

    /**
     * @var TrackLocation[]|null
     */
    #[Groups([self::GROUP_DETAILS])]
    #[SerializedName('tracking_data')]
    public ?array $trackingData = null;

    #[Groups([self::GROUP_LIST])]
    public ?\DateTimeImmutable $date = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('equipment_external_id')]
    public ?string $equipmentExternalId = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('tour_external_id')]
    public ?string $tourExternalId = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('device_id')]
    public ?string $deviceId = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('duration')]
    public ?int $duration = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('waypoint_count')]
    public ?int $waypointCount = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('tour_id')]
    public ?string $tourId = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('license_plate')]
    public ?string $licensePlate = null;

    #[Groups([self::GROUP_LIST])]
    #[SerializedName('session_id')]
    public string $sessionId;

    public static function listEntryFromEntity(
        TrackingEntity $tracking,
        ?string $tourId = null,
        ?string $licensePlate = null,
        bool $canSeeProtectedTrackings = false,
    ): self {
        $dto = new self();
        $dto->trackingId = $tracking->getId();
        $dto->equipmentExternalId = $tracking->getEquipmentExternalId();
        $dto->tourExternalId = $tracking->getTourExternalId();
        $dto->deviceId = $tracking->getDeviceId();
        $dto->tourId = $tourId;
        $dto->licensePlate = $licensePlate;
        $dto->date = $tracking->getDate();
        $dto->duration = 0;
        $dto->sessionId = $tracking->getSessionId();

        $trackingData = $tracking->getTrackingData();

        /**
         * @var TrackLocation[] $unprotectedTrackingData
         */
        $unprotectedTrackingData = array_values(array_filter(
            $trackingData,
            static fn (TrackLocation $trackLocation): bool => false === $trackLocation->protected,
        ));

        $dto->waypointCount = $canSeeProtectedTrackings ? count($trackingData) : count($unprotectedTrackingData);

        if (count($trackingData) > 1) {
            $startData = $trackingData[0];
            $endData = $trackingData[count($trackingData) - 1];
            $diff = date_diff($startData->timestamp, $endData->timestamp);
            $dto->duration += $diff->days * 24 * 60;
            $dto->duration += $diff->h * 60;
            $dto->duration += $diff->i;
            $dto->duration = abs($dto->duration);
        }

        return $dto;
    }
}
