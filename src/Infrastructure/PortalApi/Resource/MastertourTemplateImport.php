<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\MastertourTemplateImportEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\Waypoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Post(
            controller: [MastertourTemplateImportEndpoint::class, 'extractImportFile'],
            uriTemplate: '/mastertour-template-import/{type}/{fileName}', pathParameters: [
                new PathParameter(
                    name: 'type',
                    type: 'string',
                    description: 'available options: couplink',
                ),
                new PathParameter(
                    name: 'fileName',
                    type: 'string',
                    description: 'name of the file without ending, should be the externalId of the mastertour by convention',
                ),
            ],
            requestType: ContentType::BINARY,
            requestDescription: 'Binary file contents',
            normalizationContext: ['groups' => [self::GROUP_IMPORT]],
            responseOpenApiSchemaName: 'MastertourImportResultItem',
        ),
    ],
    identifier: null,
    tag: 'MastertourTemplate',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_MANAGE_MASTERTOURS->value.'")',
)]
class MastertourTemplateImport
{
    private const string GROUP_IMPORT = 'mastertour-template:import';

    #[Groups([self::GROUP_IMPORT])]
    #[SerializedName('external_id')]
    public string $externalId;

    #[Groups([self::GROUP_IMPORT])]
    #[SerializedName('mastertour_template_id')]
    public ?string $mastertourTemplateId = null;

    #[Groups([self::GROUP_IMPORT])]
    #[SerializedName('branch_id')]
    public ?string $branchId = null;

    #[Groups([self::GROUP_IMPORT])]
    public ?string $name = null;

    #[Groups([self::GROUP_IMPORT])]
    public ?string $description = null;

    /**
     * @var Waypoint[]
     */
    #[Groups([self::GROUP_IMPORT])]
    public array $waypoints = [];
}
