<?php

declare(strict_types=1);

namespace App\Infrastructure\PortalApi\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\PortalApi\HttpEndpoint\ConnectedDeviceEndpoint;
use App\Infrastructure\PortalApi\Resource\Dto\HardwareType;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ConnectedDeviceEndpoint::class, 'getHardwareTypes'],
            uriTemplate: '/connected-devices/hardware-types',
            output: HardwareType::class,
            responseOpenApiSchemaName: 'HardwareTypeListItem',
        ),
    ],
    identifier: null,
    tag: 'ConnectedDevice',
    security: 'is_granted("'.UserRole::ROLE_PORTAL_VIEW_EQUIPMENT->value.'")',
)]
class ConnectedDevice
{
}
