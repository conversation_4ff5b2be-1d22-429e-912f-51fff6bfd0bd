<?php

declare(strict_types=1);

namespace App\Infrastructure\SapGermany\Resource;

use App\Infrastructure\SapGermany\Dto\Input\TourDto;
use App\Infrastructure\SapGermany\Dto\Output\TourIngested;
use App\Infrastructure\SapGermany\HttpEndpoint\TourEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;

#[ApiResource(
    area: 'sap_germany',
    operations: [
        new Get(
            controller: [TourEndpoint::class, 'ping'],
            uriTemplate: '/ping',
            responseDescription: 'Ping successful',
            responseType: ContentType::EMPTY,
            successHttpCode: 200,
        ),
        new Post(
            controller: [TourEndpoint::class, 'tour'],
            uriTemplate: '/tour',
            input: TourDto::class,
            responseDescription: 'Tour successful imported',
            output: TourIngested::class,
            successHttpCode: 200,
        ),
        new Delete(
            controller: [TourEndpoint::class, 'invalidateTour'],
            uriTemplate: '/tour/{tourExternalId}', pathParameters: [
                new PathParameter(
                    name: 'tourExternalId',
                    type: 'string',
                    description: 'External ID of the tour to invalidate',
                ),
            ],
            responseDescription: 'Tour successfully invalidated',
            responseType: ContentType::EMPTY,
            successHttpCode: 204,
        ),
    ],
    identifier: null,
    tag: 'SAP Germany',
)]
class SapGermany
{
}
