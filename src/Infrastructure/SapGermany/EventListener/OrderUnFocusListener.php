<?php

declare(strict_types=1);

namespace App\Infrastructure\SapGermany\EventListener;

use App\Domain\Context\ActionContext;
use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Status\OrderStatus;
use App\Domain\Event\OrderUnFocusedEvent;
use App\Domain\Services\SessionService;
use App\Infrastructure\SapCommon\Dto\Output\ObjectStatusDto;
use App\Infrastructure\SapCommon\Dto\Output\ObjectType;
use App\Infrastructure\SapNetherlands\MessageQueue\AsyncDispatcher;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: OrderUnFocusedEvent::NAME)]
class OrderUnFocusListener
{
    public function __construct(
        private TenantContext $tenantContext,
        private ActionContext $actionContext,
        private AsyncDispatcher $sapMessageScheduler,
        private SessionService $sessionService,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(OrderUnFocusedEvent $event): void
    {
        if (!$this->tenantContext->getTenant()->isGermany() || $this->actionContext->isSapSyncDisabled()) {
            return;
        }

        $order = $event->getOrder();
        if (OrderStatus::STARTED !== $order->getStatus()) {
            $this->logger->error(
                'order-unfocus-event for non-started order',
                ['order' => $order->getId()],
            );

            return;
        }

        $this->sapMessageScheduler->dispatchObjectStatusMessage(
            tourId: $order->getTour()->getId(),
            objectStatusDto: ObjectStatusDto::createObjectStatusDto(
                session: $this->sessionService->getActiveDeviceSession(),
                objectType: ObjectType::ORDER,
                objectExternalId: $order->getOrderExtId(),
                subType: null,
                subTypeExternalId: null,
                uuid: $order->getId(),
                status: 'break',
                latitude: $event->getLatitude(),
                longitude: $event->getLongitude(),
                mileage: $event->getMileage(),
                timestamp: $event->getTimestamp(),
            )
        );
    }
}
