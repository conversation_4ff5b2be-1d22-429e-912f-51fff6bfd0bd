<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum\Types;

enum TaskGroupType: string
{
    case CUSTOMER = 'CUSTOMER';
    case DEPOT = 'DEPOT';
    case BRANCH = 'BRANCH';
    case DISPOSALSITE = 'DISPOSALSITE';
    case CHOICE = 'CHOICE';
    case INTERRUPTION = 'INTERRUPTION';
    case TERMINATION = 'TERMINATION';
    case NOTE = 'NOTE';
    case TOUR = 'TOUR';
    case STAFF = 'STAFF';
    case EQUIPMENT = 'EQUIPMENT';
    case VEHICLECHECK = 'VEHICLECHECK';

    case MUNICIPAL_APPROACH = 'M<PERSON><PERSON><PERSON><PERSON>_APPROACH';
    case MUNICIPAL_SERVICE = 'MUNICIPAL_SERVICE';
    case MUNICIPAL_RETURN = 'MUNICIPAL_RETURN';

    case DISPOSAL_SITE_TEMPLATE = 'DISPOSAL-SITE-TEMPLATE';
}
