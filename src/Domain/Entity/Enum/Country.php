<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum Country: string
{
    case AT = 'at';
    case BE = 'be';
    case CH = 'ch';
    case CZ = 'cz';
    case DE = 'de';
    case ES = 'es';
    case FR = 'fr';
    case GB = 'gb';
    case HU = 'hu';
    case IE = 'ie';
    case IT = 'it';
    case LI = 'li';
    case LV = 'lv';
    case LU = 'lu';
    case NL = 'nl';
    case PL = 'pl';
    case PT = 'pt';
    case RO = 'ro';
    case SI = 'si';
    case SK = 'sk';
    case SWE = 'swe';

    /**
     * @return array<string>
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
