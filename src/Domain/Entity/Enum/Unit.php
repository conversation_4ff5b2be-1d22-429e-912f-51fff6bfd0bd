<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum Unit: string
{
    case KG = 'KG';
    case T = 'T';
    case M3 = 'M3';
    case H = 'H';
    case L = 'L';
    case P = 'P';
    case M = 'M';
    case STD = 'STD';

    public function getTranslationTag(): string
    {
        return match ($this) {
            self::KG => '{{unit/kg}}',
            self::L => '{{unit/liter}}',
            self::H, self::STD => '{{unit/hours}}',
            self::T => '{{unit/tons}}',
            self::M3 => '{{unit/m3}}',
            self::P => '{{unit/piece}}',
            self::M => '{{unit/meter}}',
        };
    }
}
