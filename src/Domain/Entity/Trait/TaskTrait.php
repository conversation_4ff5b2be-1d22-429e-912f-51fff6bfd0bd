<?php

declare(strict_types=1);

namespace App\Domain\Entity\Trait;

use App\Domain\Entity\AdditionalServiceConfig;
use App\Domain\Entity\ValueObject\Element;
use App\Exception\TemplateNotFoundException;

trait TaskTrait
{
    public function getElementById(string $uuid): ?Element
    {
        foreach ($this->getElements() as $element) {
            if ($element->id === $uuid) {
                return $element;
            }
        }

        return null;
    }

    /**
     * @param bool|string|int|float|array<string|object>|null $elementValue
     */
    public function generateNewElementFromExisting(
        string $existingElementId,
        bool|string|int|float|array|null $elementValue = null,
        ?AdditionalServiceConfig $additionalService = null,
    ): Element {
        $existingElement = $this->getElementById($existingElementId);

        if (null === $existingElement) {
            throw new TemplateNotFoundException('Could not find referenced element template.', $existingElementId);
        }

        return $existingElement
            ->clone()
            ->withOptions($existingElement->generateElementOptionsMap($additionalService))
            ->withValue($elementValue);
    }
}
