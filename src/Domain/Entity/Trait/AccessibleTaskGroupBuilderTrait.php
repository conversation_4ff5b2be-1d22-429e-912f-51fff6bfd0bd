<?php

declare(strict_types=1);

namespace App\Domain\Entity\Trait;

use App\Domain\Entity\AccessibleNote;
use App\Domain\Entity\AccessibleTermination;
use App\Domain\Entity\Util\EntityContainer;

trait AccessibleTaskGroupBuilderTrait
{
    use RuleBuilderTrait;

    private function createAccessibleTaskGroups(AccessibleNote|AccessibleTermination $accessible): void
    {
        $createdEntities = new EntityContainer();

        foreach ($this->getDefaultTaskGroups() as $defaultTaskGroup) {
            $accessibleTaskGroup = $defaultTaskGroup->createAccessibleTaskGroup($createdEntities);

            $createdEntities->add($defaultTaskGroup->getId(), $accessibleTaskGroup);

            $accessible->addAccessibleTaskGroup($accessibleTaskGroup);
        }

        $this->mapAccessibleRules(
            defaultTaskGroupCollection: $this->getDefaultTaskGroups(),
            createdEntities: $createdEntities,
        );
    }
}
