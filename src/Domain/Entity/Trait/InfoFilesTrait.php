<?php

declare(strict_types=1);

namespace App\Domain\Entity\Trait;

use App\Domain\Entity\ValueObject\InfoFile;
use App\Domain\Services\Domain;
use App\Domain\Services\ObjectStorage\ObjectPrefix;
use Doctrine\ORM\Mapping as ORM;

trait InfoFilesTrait
{
    /**
     * @var InfoFile[]
     */
    #[ORM\Column(type: 'json_document', options: ['default' => '[]', 'jsonb' => true])]
    private array $infoFiles = [];

    /**
     * @return InfoFile[]
     */
    public function getInfoFiles(): array
    {
        return $this->infoFiles;
    }

    /**
     * @param InfoFile[] $infoFiles
     */
    public function setInfoFiles(array $infoFiles): self
    {
        $this->infoFiles = $infoFiles;

        return $this;
    }

    /**
     * @param InfoFile[] $newInfoFiles
     */
    public function loadNewInfoFiles(array $newInfoFiles): void
    {
        $existingIdentifiers = array_column($this->infoFiles, 'identifier');
        $newIdentifiers = array_column($newInfoFiles, 'identifier');

        if ([] !== $this->infoFiles) {
            $this->deleteObsoleteInfoFiles($newIdentifiers);
        }

        foreach ($newInfoFiles as $newInfoFile) {
            if (!in_array($newInfoFile->identifier, $existingIdentifiers)) {
                $this->infoFiles[] = $newInfoFile;
            }
        }

        $this->infoFiles = array_values($this->infoFiles);

        if (array_any($this->infoFiles, static fn (InfoFile $infoFile): bool => null === $infoFile->uuid)) {
            Domain::events()->dispatchInfoFileDownload($this);
        }
    }

    /**
     * @param array<string> $newIdentifiers
     */
    private function deleteObsoleteInfoFiles(array $newIdentifiers): void
    {
        $tenantShortcut = $this->getTenant()->value;
        $filesToDelete = [];

        foreach ($this->infoFiles as $index => $existingInfoFile) {
            if (!in_array($existingInfoFile->identifier, $newIdentifiers)) {
                if (null !== $existingInfoFile->uuid) {
                    $filesToDelete[] = $tenantShortcut.'/'.ObjectPrefix::APP_DOCUMENTS->value.$existingInfoFile->uuid;
                }

                unset($this->infoFiles[$index]);
            }
        }

        $this->infoFiles = array_values($this->infoFiles);

        $this->removeAdditionalInformationContainingFiles($filesToDelete);
    }
}
