<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Interfaces\TaskTemplate;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TaskTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\Util\EntityContainer;
use App\Domain\Repository\DefaultTaskRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: DefaultTaskRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class DefaultTask implements EntityInterface, TaskTemplate, HasTenant
{
    use CommonTrait;
    use TenantTrait;
    use TaskTrait;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $externalId = null;

    #[ORM\Column(length: 150, nullable: false)]
    private string $type;

    #[ORM\Column(length: 255, nullable: false)]
    private string $name;

    #[ORM\Column]
    private bool $activatedBySapData = false;

    #[ORM\Column]
    private bool $repeatable = false;

    #[ORM\Column(nullable: false, options: ['default' => false])]
    private bool $sapAction = false;

    /**
     * @var ValueObject\Element[]
     */
    #[ORM\Column(type: 'json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private array $elementItems = [];

    /**
     * @var ValueObject\TaskAction[]
     */
    #[ORM\Column(type: 'json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private array $taskActions = [];

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getType(): string
    {
        return $this->type ?? throw new \RuntimeException('Type is not set');
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getName(): string
    {
        return $this->name ?? throw new \RuntimeException('Task name is not set');
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function isActivatedBySapData(): bool
    {
        return $this->activatedBySapData;
    }

    public function setActivatedBySapData(bool $activatedBySapData): self
    {
        $this->activatedBySapData = $activatedBySapData;

        return $this;
    }

    /**
     * @return ValueObject\Element[]
     */
    public function getElements(): array
    {
        return $this->elementItems;
    }

    /**
     * @return ValueObject\TaskAction[]
     */
    public function getTaskActions(): array
    {
        return $this->taskActions;
    }

    public function createBasicTask(): Task
    {
        return new Task()
            ->setName($this->getName())
            ->setExternalId($this->getExternalId())
            ->setActivatedBySapData($this->isActivatedBySapData())
            ->setType($this->getType())
            ->setSapAction($this->isSapAction())
            ->setRepeatable($this->isRepeatable())
            ->setTaskActions($this->getTaskActions())
        ;
    }

    public function isRepeatable(): bool
    {
        return $this->repeatable;
    }

    public function setRepeatable(bool $repeatable): static
    {
        $this->repeatable = $repeatable;

        return $this;
    }

    public function createFullTask(EntityContainer $createdEntities = new EntityContainer()): Task
    {
        $task = $this->createBasicTask();

        foreach ($this->getElements() as $defaultElement) {
            $element = $defaultElement->clone();
            $task->addElement($element);
            $createdEntities->add($defaultElement->id, $element);
        }

        return $task;
    }

    public function createAccessibleTask(EntityContainer $createdEntities = new EntityContainer()): AccessibleTask
    {
        $accessible = new AccessibleTask()
            ->setExternalId($this->getExternalId())
            ->setName($this->getName())
            ->setType($this->getType())
            ->setActivatedBySapData($this->isActivatedBySapData())
            ->setRepeatable($this->isRepeatable())
            ->setSapAction($this->isSapAction())
            ->setTaskActions($this->getTaskActions())
        ;

        foreach ($this->getElements() as $defaultElement) {
            $element = $defaultElement->clone();
            $accessible->addElement($element);
            $createdEntities->add($defaultElement->id, $element);
        }

        return $accessible;
    }

    public function isSapAction(): bool
    {
        return $this->sapAction;
    }

    public function setSapAction(bool $sapAction): self
    {
        $this->sapAction = $sapAction;

        return $this;
    }
}
