<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\SessionRepository;
use App\Domain\Services\Domain;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: SessionRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class Session implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 255)]
    private string $deviceUniqueId;

    #[ORM\Column]
    private \DateTimeImmutable $start;

    // end is a reserved keyword in PostgreSQL
    #[ORM\Column(name: '"end"', nullable: true)]
    private ?\DateTimeImmutable $end = null;

    /**
     * @var Collection<int, SessionUser>
     */
    #[ORM\OneToMany(targetEntity: SessionUser::class, mappedBy: 'session', orphanRemoval: true)]
    #[ORM\OrderBy(['modifiedAt' => 'DESC'])]
    private Collection $sessionUsers;

    /**
     * @var Collection<int, SessionEquipment>
     */
    #[ORM\OneToMany(targetEntity: SessionEquipment::class, mappedBy: 'session', orphanRemoval: true)]
    #[ORM\OrderBy(['modifiedAt' => 'DESC'])]
    private Collection $sessionEquipments;

    /**
     * @var Collection<int, Interruption>
     */
    #[ORM\OneToMany(targetEntity: Interruption::class, mappedBy: 'session')]
    private Collection $interruptions;

    /**
     * @var Collection<int, SessionTour>
     */
    #[ORM\OneToMany(targetEntity: SessionTour::class, mappedBy: 'session', cascade: ['persist'], orphanRemoval: true)]
    private Collection $sessionTours;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->sessionUsers = new ArrayCollection();
        $this->sessionEquipments = new ArrayCollection();
        $this->interruptions = new ArrayCollection();
        $this->sessionTours = new ArrayCollection();
    }

    public function getDeviceUniqueId(): string
    {
        return $this->deviceUniqueId;
    }

    public function setDeviceUniqueId(string $deviceUniqueId): self
    {
        $this->deviceUniqueId = $deviceUniqueId;

        return $this;
    }

    public function getStart(): ?\DateTimeImmutable
    {
        return $this->start;
    }

    public function setStart(\DateTimeInterface $start): self
    {
        $this->start = \DateTimeImmutable::createFromInterface($start);

        return $this;
    }

    public function getEnd(): ?\DateTimeImmutable
    {
        return $this->end;
    }

    public function setEnd(?\DateTimeInterface $end): self
    {
        $this->end = null !== $end ? \DateTimeImmutable::createFromInterface($end) : null;

        return $this;
    }

    /**
     * @return Collection<int, SessionUser>
     */
    public function getSessionUsers(): Collection
    {
        return $this->sessionUsers;
    }

    public function addSessionUser(SessionUser $sessionUser): self
    {
        if (!$this->sessionUsers->contains($sessionUser)) {
            $this->sessionUsers->add($sessionUser);
            $sessionUser->setSession($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, SessionEquipment>
     */
    public function getSessionEquipments(): Collection
    {
        return $this->sessionEquipments;
    }

    public function addSessionEquipment(SessionEquipment $sessionEquipment): self
    {
        if (!$this->sessionEquipments->contains($sessionEquipment)) {
            $this->sessionEquipments->add($sessionEquipment);
            $sessionEquipment->setSession($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, Interruption>
     */
    public function getInterruptions(): Collection
    {
        return $this->interruptions;
    }

    public function addInterruption(Interruption $interruption): static
    {
        if (!$this->interruptions->contains($interruption)) {
            $this->interruptions->add($interruption);
            $interruption->setSession($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, SessionTour>
     */
    public function getSessionTours(): Collection
    {
        return $this->sessionTours;
    }

    public function getLastTour(): ?Tour
    {
        $sessionTours = $this->getSessionTours();

        if ($sessionTours->isEmpty()) {
            return null;
        }

        $sessionToursByEndDate = [];

        foreach ($sessionTours as $sessionTour) {
            $endTimestamp = $sessionTour->getEnd()?->getTimestamp();

            if (null === $endTimestamp) {
                return $sessionTour->getTour();
            }

            $sessionToursByEndDate[$endTimestamp] = $sessionTour;
        }

        ksort($sessionToursByEndDate);
        $lastSession = end($sessionToursByEndDate);
        assert($lastSession instanceof SessionTour);

        return $lastSession->getTour();
    }

    /**
     * @return array<SessionUser>
     */
    public function getActiveSessionUsers(): array
    {
        $activeSessionUsers = [];

        foreach ($this->getSessionUsers() as $sessionUser) {
            if (null === $sessionUser->getEnd()) {
                $activeSessionUsers[] = $sessionUser;
            }
        }

        return $activeSessionUsers;
    }

    /**
     * @return array<User>
     */
    public function getActiveUsers(): array
    {
        $activeUsers = [];

        foreach ($this->getSessionUsers() as $sessionUser) {
            if (null === $sessionUser->getEnd()) {
                $activeUsers[] = $sessionUser->getUser();
            }
        }

        return $activeUsers;
    }

    /**
     * @return array<SessionTour>
     */
    public function getActiveSessionTours(): array
    {
        $unfinishedSessionTours = [];

        foreach ($this->getSessionTours() as $sessionTour) {
            if (null === $sessionTour->getEnd()) {
                $unfinishedSessionTours[] = $sessionTour;
            }
        }

        return $unfinishedSessionTours;
    }

    /**
     * @return array<SessionEquipment>
     */
    public function getActiveSessionEquipments(): array
    {
        $activeSessionEquipments = [];

        foreach ($this->getSessionEquipments() as $sessionEquipment) {
            if (null === $sessionEquipment->getEnd()) {
                $activeSessionEquipments[] = $sessionEquipment;
            }
        }

        return $activeSessionEquipments;
    }

    public function getFirstActiveTruck(): ?Equipment
    {
        foreach ($this->getSessionEquipments() as $sessionEquipment) {
            if (null === $sessionEquipment->getEnd()
                && 'truck' === $sessionEquipment->getEquipment()->getType()->getCategory()->value) {
                return $sessionEquipment->getEquipment();
            }
        }

        return null;
    }

    /**
     * @return array<Interruption>
     */
    public function getActiveInterruptions(): array
    {
        $activeInterruptions = [];

        foreach ($this->getInterruptions() as $interruption) {
            if (null === $interruption->getEnd()) {
                $activeInterruptions[] = $interruption;
            }
        }

        return $activeInterruptions;
    }

    public function isActiveLongerThan(int $seconds): bool
    {
        $start = $this->getStart();

        if (null === $start) {
            return false;
        }

        $sessionDuration = time() - $start->getTimestamp();

        return $sessionDuration > $seconds;
    }

    public function end(): void
    {
        $activeInterruptions = $this->getActiveInterruptions();

        foreach ($activeInterruptions as $activeInterruption) {
            $activeInterruption->complete();
        }

        $activeSessionEquipments = $this->getActiveSessionEquipments();

        foreach ($activeSessionEquipments as $activeSessionEquipment) {
            $activeSessionEquipment->setEnd(new \DateTimeImmutable());
            $activeSessionEquipment->getEquipment()->abort();
        }

        $activeSessionTours = $this->getActiveSessionTours();

        foreach ($activeSessionTours as $activeSessionTour) {
            $activeSessionTour->setEnd(new \DateTimeImmutable());
            $activeSessionTour->getTour()->return();
        }

        $activeSessionUsers = $this->getActiveSessionUsers();

        foreach ($activeSessionUsers as $activeSessionUser) {
            $activeSessionUser->getStaff()->abort();
        }

        $this->setEnd(new \DateTimeImmutable());
    }

    public function startTour(Tour $tour): void
    {
        foreach ($this->sessionTours as $sessionTour) {
            if ($sessionTour->getTour()->getId() === $tour->getId()) {
                return;
            }
        }

        $this->sessionTours->add(
            new SessionTour()
                ->setSession($this)
                ->setTour($tour)
                ->setStart(new \DateTimeImmutable())
        );
    }

    public function delete(): void
    {
        Domain::events()->dispatchEntityDeleteEvent($this);

        foreach ($this->sessionTours as $sessionTour) {
            $sessionTour->delete();
        }

        foreach ($this->sessionUsers as $sessionUser) {
            $sessionUser->delete();
        }

        foreach ($this->sessionEquipments as $sessionEquipment) {
            $sessionEquipment->delete();
        }

        foreach ($this->interruptions as $interruption) {
            $interruption->delete();
        }
    }

    /**
     * @return string[]
     */
    public function getSessionBranchIds(): array
    {
        $userBranchIds = array_map(
            fn (SessionUser $sessionUser): string =>
                $sessionUser->getUser()->getStaff()?->getBranch()->getId()
                    ?: throw new \RuntimeException('invalid session-user without staff'),
            array: $this->getActiveSessionUsers(),
        );

        $equipmentBranchIds = array_map(
            fn (SessionEquipment $sessionEquipment): string =>
            $sessionEquipment->getEquipment()->getBranch()->getId(),
            array: $this->getActiveSessionEquipments(),
        );

        return array_unique(
            array_merge($userBranchIds, $equipmentBranchIds)
        );
    }
}
