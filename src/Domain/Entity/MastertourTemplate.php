<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\Waypoint;
use App\Domain\Repository\FaqRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: FaqRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['search', 'tenant'])]
#[ORM\UniqueConstraint(fields: ['tenant', 'externalId'])]
#[ORM\Index(fields: ['tenant'])]
class MastertourTemplate implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 255, nullable: false)]
    private string $name;

    #[ORM\Column(length: 255, nullable: false)]
    private string $externalId;

    /**
     * @var Waypoint[]
     */
    #[ORM\Column(type: 'json_document', options: ['default' => '[]', 'jsonb' => true])]
    private array $waypoints = [];

    #[ORM\ManyToOne(inversedBy: 'mastertourTemplates')]
    #[ORM\JoinColumn(nullable: false)]
    private Branch $branch;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(nullable: false)]
    private int $distanceInMeters;

    #[ORM\Column(length: 512, nullable: false, options: ['default' => ''])]
    private string $search;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    /**
     * @return Waypoint[]
     */
    public function getWaypoints(): array
    {
        return $this->waypoints;
    }

    /**
     * @param Waypoint[] $waypoints
     */
    public function setWaypoints(array $waypoints): self
    {
        $this->waypoints = $waypoints;

        return $this;
    }

    public function addWaypoint(Waypoint $waypoint): self
    {
        $this->waypoints[] = $waypoint;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        $this->updateSearch();

        return $this;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;
        $this->updateSearch();

        return $this;
    }

    public function getBranch(): Branch
    {
        return $this->branch;
    }

    public function setBranch(Branch $branch): self
    {
        $this->branch = $branch;

        return $this;
    }

    public function getWaypoint(string $waypointId): ?Waypoint
    {
        foreach ($this->getWaypoints() as $waypoint) {
            if ($waypoint->id === $waypointId) {
                return $waypoint;
            }
        }

        return null;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getDistanceInMeters(): int
    {
        return $this->distanceInMeters;
    }

    public function setDistanceInMeters(int $distanceInMeters): self
    {
        $this->distanceInMeters = $distanceInMeters;

        return $this;
    }

    private function updateSearch(): void
    {
        $this->search = implode(' ', [$this->name ?? '', $this->externalId ?? '']);
    }
}
