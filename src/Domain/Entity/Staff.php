<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Annotations\StatusIn;
use App\Domain\Entity\Enum\Status\StaffStatus;
use App\Domain\Entity\Enum\Types\PersonnelType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasStatus;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\StatusTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\StaffRepository;
use App\Domain\Services\Domain;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

/**
 * @implements HasStatus<StaffStatus>
 */
#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: StaffRepository::class)]
#[StatusIn(StaffStatus::class)]
#[ORM\Index(fields: ['search', 'tenant'])]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class Staff implements EntityInterface, HasTenant, HasStatus
{
    use CommonTrait;
    use TenantTrait;
    /** @use StatusTrait<StaffStatus> */
    use StatusTrait;

    private const int DAKO_FILE_UPLOAD_DAYS = 7;

    #[ORM\Column(length: 50, nullable: false)]
    private string $externalId;

    /**
     * @var Collection<int, StaffVersion>
     */
    #[ORM\OneToMany(targetEntity: StaffVersion::class, mappedBy: 'staff', cascade: ['persist'], orphanRemoval: true)]
    #[ORM\OrderBy(['createdAt' => 'ASC'])]
    private Collection $staffVersions;

    #[ORM\OneToOne(mappedBy: 'staff')]
    private ?User $user = null;

    #[ORM\ManyToOne(inversedBy: 'staffs')]
    #[ORM\JoinColumn(nullable: false)]
    private Branch $branch;

    #[ORM\Column(length: 20, nullable: false, enumType: PersonnelType::class)]
    private PersonnelType $personnelType;

    #[ORM\Column(length: 255, nullable: false)]
    private string $firstname;

    #[ORM\Column(length: 255, nullable: false)]
    private string $lastname;

    #[ORM\Column(length: 1000, nullable: false)]
    private string $search;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $lastDddFileUpload = null;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->status = StaffStatus::IDLE->value;
        $this->statusChangedAt = new \DateTimeImmutable();
        $this->staffVersions = new ArrayCollection();
    }

    public function getBranch(): Branch
    {
        return $this->branch;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        $this->updateSearch();

        return $this;
    }

    public function getUser(): User
    {
        return $this->user ?? throw new \RuntimeException('User is not set in Staff');
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function addStaffVersion(StaffVersion $staffVersion): self
    {
        $this->staffVersions->add($staffVersion);
        $staffVersion->setStaff($this);

        $this->branch = $staffVersion->getBranch();
        $this->personnelType = $staffVersion->getPersonnelType();
        $this->firstname = $staffVersion->getFirstname();
        $this->lastname = $staffVersion->getLastname();
        $this->updateSearch();

        return $this;
    }

    public function getPersonnelType(): PersonnelType
    {
        return $this->personnelType;
    }

    public function getFirstname(): string
    {
        return $this->firstname;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function getLastSession(): ?Session
    {
        $sessionUsers = $this->getUser()->getSessionUsers();

        if ($sessionUsers->isEmpty()) {
            return null;
        }

        $userSessionsByEndDate = [];

        foreach ($sessionUsers as $sessionUser) {
            $endTimeStamp = $sessionUser->getEnd()?->getTimestamp();

            if (null === $endTimeStamp) {
                return $sessionUser->getSession();
            }

            $userSessionsByEndDate[$endTimeStamp] = $sessionUser->getSession();
        }

        ksort($userSessionsByEndDate);

        $lastSession = end($userSessionsByEndDate);
        assert($lastSession instanceof Session);

        return $lastSession;
    }

    /**
     * @return array<Session>
     */
    public function getActiveSessions(): array
    {
        $activeSessions = [];

        foreach ($this->getUser()->getSessionUsers() as $sessionUser) {
            if (null === $sessionUser->getEnd()) {
                $activeSessions[] = $sessionUser->getSession();
            }
        }

        return $activeSessions;
    }

    public function abort(): void
    {
        Domain::workflow()->applyForStaff($this, 'abort');
    }

    private function updateSearch(): void
    {
        $this->search = sprintf('%s %s %s', $this->firstname ?? '', $this->lastname ?? '', $this->externalId ?? '');

        Domain::events()->dispatchSearchColumnUpdateEvent($this);
    }

    public function getLastDddFileUpload(): ?\DateTimeImmutable
    {
        return $this->lastDddFileUpload;
    }

    public function setLastDddFileUpload(?\DateTimeImmutable $lastDddFileUpload): Staff
    {
        $this->lastDddFileUpload = $lastDddFileUpload;

        return $this;
    }

    public function dddFileUploadRequired(): bool
    {
        if (null === $this->getBranch()->getDakoAccount()) {
            return false;
        }

        if (null === $this->getLastDddFileUpload()) {
            return true;
        }

        return $this->getLastDddFileUpload()
                ->modify('+'.self::DAKO_FILE_UPLOAD_DAYS.' days') < new \DateTimeImmutable();
    }

    public function delete(): void
    {
        foreach ($this->staffVersions as $staffVersion) {
            $staffVersion->delete();
        }

        Domain::events()->dispatchEntityDeleteEvent($this);
    }

    public function focus(): self
    {
        Domain::events()->dispatchStaffFocused($this);

        return $this;
    }
}
