<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Country;
use App\Domain\Entity\Enum\Types\LocationType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\LocationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: LocationRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class Location implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 50)]
    private string $externalId;

    /**
     * @var Collection<int, LocationVersion>
     */
    #[ORM\OneToMany(targetEntity: LocationVersion::class, mappedBy: 'location', cascade: ['persist'], orphanRemoval: true)]
    #[ORM\OrderBy(['createdAt' => 'ASC'])]
    private Collection $locationVersions;

    #[ORM\Column(length: 20, nullable: false, enumType: LocationType::class)]
    private LocationType $type;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $phone = null;

    #[ORM\Column(length: 60, nullable: false)]
    private string $street;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $houseNumber = null;

    #[ORM\Column(length: 10, nullable: false)]
    private string $postalCode;

    #[ORM\Column(length: 40, nullable: false)]
    private string $city;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $district = null;

    #[ORM\Column(length: 3, nullable: false, enumType: Country::class)]
    private Country $country;

    #[ORM\Column(length: 60, nullable: true)]
    private ?string $state = null;

    #[ORM\Column(nullable: true)]
    private ?float $latitude = null;

    #[ORM\Column(nullable: true)]
    private ?float $longitude = null;

    #[ORM\Column(length: 255, nullable: true, options: ['default' => null])]
    private ?string $name = null;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->locationVersions = new ArrayCollection();
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function addLocationVersion(LocationVersion $locationVersion): self
    {
        $this->locationVersions->add($locationVersion);
        $locationVersion->setLocation($this);

        $this->type = $locationVersion->getType();
        $this->phone = $locationVersion->getPhone();
        $this->street = $locationVersion->getStreet();
        $this->houseNumber = $locationVersion->getHouseNumber();
        $this->postalCode = $locationVersion->getPostalCode();
        $this->city = $locationVersion->getCity();
        $this->district = $locationVersion->getDistrict();
        $this->country = $locationVersion->getCountry();
        $this->state = $locationVersion->getState();
        $this->latitude = $locationVersion->getLatitude();
        $this->longitude = $locationVersion->getLongitude();
        $this->name = $locationVersion->getName();

        return $this;
    }

    public function getType(): LocationType
    {
        return $this->type;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getStreet(): string
    {
        return $this->street;
    }

    public function getHouseNumber(): ?string
    {
        return $this->houseNumber;
    }

    public function getPostalCode(): string
    {
        return $this->postalCode;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getDistrict(): ?string
    {
        return $this->district;
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getName(): ?string
    {
        return $this->name;
    }
}
