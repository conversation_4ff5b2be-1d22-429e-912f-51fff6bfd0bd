<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\DakoAccountRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: DakoAccountRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class DakoAccount implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 255)]
    private string $name;

    #[ORM\Column(length: 255)]
    private string $dakoId;

    /**
     * @var Collection<int, Branch>
     */
    #[ORM\OneToMany(targetEntity: Branch::class, mappedBy: 'dakoAccount')]
    private Collection $branches;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->branches = new ArrayCollection();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDakoId(): string
    {
        return $this->dakoId;
    }

    public function setDakoId(string $dakoId): self
    {
        $this->dakoId = $dakoId;

        return $this;
    }

    /**
     * @param Branch[] $branches
     */
    public function setBranches(array $branches): self
    {
        foreach ($this->branches as $branch) {
            if (!in_array($branch, $branches)) {
                $this->removeBranch($branch);
            }
        }

        foreach ($branches as $branch) {
            if (!in_array($branch, $this->branches->toArray())) {
                $this->addBranch($branch);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Branch>
     */
    public function getBranches(): Collection
    {
        return $this->branches;
    }

    /**
     * @return string[]
     */
    public function getBranchIds(): array
    {
        return $this->getBranches()
            ->map(fn (Branch $branch): string => $branch->getId())
            ->toArray();
    }

    private function addBranch(Branch $branch): self
    {
        if (!$this->branches->contains($branch)) {
            $this->branches->add($branch);
            $branch->setDakoAccount($this);
        }

        return $this;
    }

    private function removeBranch(Branch $branch): self
    {
        if ($this->branches->contains($branch) && $branch->getDakoAccount() === $this) {
            $this->branches->removeElement($branch);
            $branch->setDakoAccount(null);
        }

        return $this;
    }
}
