<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\DefaultTerminationRelationRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: DefaultTerminationRelationRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class DefaultTerminationRelation implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\ManyToOne(inversedBy: 'defaultTerminationRelations')]
    #[ORM\JoinColumn(nullable: false)]
    private DefaultTermination $defaultTermination;

    #[ORM\Column]
    private int $sequenceNumber = 0;

    #[ORM\ManyToOne(inversedBy: 'defaultTerminationRelations')]
    private ?TourDataConfig $tourDataConfig = null;

    #[ORM\ManyToOne(inversedBy: 'defaultTerminationRelations')]
    private ?OrderTypeConfig $orderTypeConfig = null;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getDefaultTermination(): DefaultTermination
    {
        return $this->defaultTermination ?? throw new \RuntimeException('DefaultTermination not set');
    }

    public function setDefaultTermination(DefaultTermination $defaultTermination): self
    {
        $this->defaultTermination = $defaultTermination;

        return $this;
    }

    public function getSequenceNumber(): int
    {
        return $this->sequenceNumber;
    }

    public function setSequenceNumber(int $sequenceNumber): self
    {
        $this->sequenceNumber = $sequenceNumber;

        return $this;
    }

    public function getTourDataConfig(): ?TourDataConfig
    {
        return $this->tourDataConfig;
    }

    public function setTourDataConfig(?TourDataConfig $tourDataConfig): self
    {
        $this->tourDataConfig = $tourDataConfig;

        return $this;
    }

    public function getOrderTypeConfig(): ?OrderTypeConfig
    {
        return $this->orderTypeConfig;
    }

    public function setOrderTypeConfig(?OrderTypeConfig $orderTypeConfig): self
    {
        $this->orderTypeConfig = $orderTypeConfig;

        return $this;
    }

    public function createAccessible(): AccessibleTerminationRelation
    {
        return new AccessibleTerminationRelation()
            ->setSequenceNumber($this->getSequenceNumber())
            ->setAccessibleTermination($this->getDefaultTermination()->createAccessibleTermination())
        ;
    }

    public function createAccessibleFromExisting(
        ?AccessibleTermination $accessibleTermination = null,
    ): AccessibleTerminationRelation {
        $accessibleTermination ??= $this->getDefaultTermination()->createAccessibleTermination();

        return new AccessibleTerminationRelation()
            ->setSequenceNumber($this->getSequenceNumber())
            ->setAccessibleTermination($accessibleTermination)
        ;
    }
}
