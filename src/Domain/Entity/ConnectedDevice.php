<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Types\ConnectionType;
use App\Domain\Entity\Enum\Types\HardwareType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ConnectedDeviceRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: ConnectedDeviceRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class ConnectedDevice implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 17, nullable: false)]
    private string $macAddress;

    #[ORM\Column(length: 30, nullable: false, enumType: HardwareType::class)]
    private HardwareType $hardwareType;

    #[ORM\Column(length: 30, nullable: false, enumType: ConnectionType::class)]
    private ConnectionType $connectionType;

    #[ORM\Column(length: 30, nullable: false)]
    private string $pin;

    #[ORM\ManyToOne(inversedBy: 'connectedDevices')]
    #[ORM\JoinColumn(nullable: false)]
    private Equipment $equipment;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getMacAddress(): string
    {
        return $this->macAddress;
    }

    public function setMacAddress(string $macAddress): self
    {
        $this->macAddress = $macAddress;

        return $this;
    }

    public function getHardwareType(): HardwareType
    {
        return $this->hardwareType;
    }

    public function setHardwareType(HardwareType $hardwareType): self
    {
        $this->hardwareType = $hardwareType;

        return $this;
    }

    public function getConnectionType(): ConnectionType
    {
        return $this->connectionType;
    }

    public function setConnectionType(ConnectionType $connectionType): self
    {
        $this->connectionType = $connectionType;

        return $this;
    }

    public function getPin(): string
    {
        return $this->pin;
    }

    public function setPin(string $pin): self
    {
        $this->pin = $pin;

        return $this;
    }

    public function getEquipment(): Equipment
    {
        return $this->equipment;
    }

    public function setEquipment(Equipment $equipment): self
    {
        $this->equipment = $equipment;

        return $this;
    }
}
