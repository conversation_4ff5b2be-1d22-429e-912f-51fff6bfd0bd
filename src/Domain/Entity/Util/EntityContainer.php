<?php

declare(strict_types=1);

namespace App\Domain\Entity\Util;

class EntityContainer
{
    /**
     * @phpstan-var array<class-string, array<string, object>>
     */
    private array $entitiesByTypeAndReference = [];

    /**
     * @template T of object
     *
     * @phpstan-param T $entity
     */
    public function add(string $reference, object $entity): void
    {
        $this->entitiesByTypeAndReference[$entity::class][$reference] = $entity;
    }

    /**
     * @template T of object
     *
     * @param class-string<T> $className
     */
    public function has(string $className, string $reference): bool
    {
        return isset($this->entitiesByTypeAndReference[$className][$reference]);
    }

    /**
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @phpstan-return T|null
     */
    public function get(string $className, string $reference): ?object
    {
        if (isset($this->entitiesByTypeAndReference[$className][$reference])) {
            /** @phpstan-var T $object */
            $object = $this->entitiesByTypeAndReference[$className][$reference];

            return $object;
        }

        return null;
    }
}
