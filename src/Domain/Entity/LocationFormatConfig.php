<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Country;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\LocationFormatConfigRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: LocationFormatConfigRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class LocationFormatConfig implements HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10, enumType: Country::class)]
    private Country $country;

    #[ORM\Column]
    private bool $highlight = false;

    #[ORM\Column]
    private int $sequence = 0;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $icon = null;

    #[ORM\Column]
    private bool $alsoFrontView = false;

    #[ORM\Column(type: Types::TEXT, nullable: false)]
    private string $formatString;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(Country $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function isHighlight(): bool
    {
        return $this->highlight;
    }

    public function setHighlight(bool $highlight): self
    {
        $this->highlight = $highlight;

        return $this;
    }

    public function getSequence(): int
    {
        return $this->sequence;
    }

    public function setSequence(int $sequence): self
    {
        $this->sequence = $sequence;

        return $this;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function setIcon(?string $icon): self
    {
        $this->icon = $icon;

        return $this;
    }

    public function isAlsoFrontView(): bool
    {
        return $this->alsoFrontView;
    }

    public function setAlsoFrontView(bool $alsoFrontView): self
    {
        $this->alsoFrontView = $alsoFrontView;

        return $this;
    }

    public function getFormatString(): string
    {
        return $this->formatString ?? throw new \RuntimeException('FormatString not set in LocationFormatConfig');
    }

    public function setFormatString(string $formatString): self
    {
        $this->formatString = $formatString;

        return $this;
    }
}
