<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\RuleEffect;
use App\Domain\Entity\Enum\RuleLogic;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\DeleteFlagTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\Rule;
use App\Domain\Repository\TaskRelationRepository;
use App\Domain\Services\Domain;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: TaskRelationRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class TaskRelation implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;
    use DeleteFlagTrait;

    #[ORM\Column]
    private int $sequenceNumber = 0;

    #[ORM\ManyToOne(inversedBy: 'taskRelations')]
    private ?TaskGroup $taskGroup = null;

    #[ORM\ManyToOne(cascade: ['persist'], inversedBy: 'taskRelations')]
    #[ORM\JoinColumn(nullable: false)]
    private Task $task;

    #[ORM\Column(length: 10, nullable: false, enumType: RuleLogic::class)]
    private RuleLogic $ruleLogic = RuleLogic::AND;

    #[ORM\Column(length: 15, nullable: false, enumType: RuleEffect::class)]
    private RuleEffect $ruleEffect = RuleEffect::DISABLED;

    #[ORM\Column(length: 15, nullable: false, enumType: RuleEffect::class)]
    private RuleEffect $ruleDefault = RuleEffect::ENABLED;

    /**
     * @var Rule[]
     */
    #[ORM\Column(type: 'json_document', options: ['default' => '[]'])]
    private array $rules = [];

    #[ORM\Column]
    private bool $optional = false;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getSequenceNumber(): int
    {
        return $this->sequenceNumber;
    }

    public function setSequenceNumber(int $sequenceNumber): self
    {
        $this->sequenceNumber = $sequenceNumber;

        return $this;
    }

    public function getTaskGroup(): TaskGroup
    {
        return $this->taskGroup ?? throw new \RuntimeException('TaskGroup is not set in TaskRelation');
    }

    public function setTaskGroup(TaskGroup $taskGroup): self
    {
        $this->taskGroup = $taskGroup;

        return $this;
    }

    public function getTask(): Task
    {
        return $this->task ?? throw new \RuntimeException('Task is not set in TaskRelation');
    }

    public function setTask(Task $task): self
    {
        $this->task = $task;

        return $this;
    }

    public function createNewFromThisInstance(): self
    {
        return new self()
            ->setSequenceNumber($this->getSequenceNumber())
            ->setTask($this->getTask()->createNewFromThisInstance())
        ;
    }

    /**
     * @return Rule[]
     */
    public function getRules(): array
    {
        return $this->rules;
    }

    public function addRule(Rule $rule): self
    {
        $this->rules[] = $rule;

        return $this;
    }

    public function getRuleLogic(): RuleLogic
    {
        return $this->ruleLogic;
    }

    public function setRuleLogic(RuleLogic $ruleLogic): self
    {
        $this->ruleLogic = $ruleLogic;

        return $this;
    }

    public function getRuleEffect(): RuleEffect
    {
        return $this->ruleEffect;
    }

    public function setRuleEffect(RuleEffect $ruleEffect): self
    {
        $this->ruleEffect = $ruleEffect;

        return $this;
    }

    public function getRuleDefault(): RuleEffect
    {
        return $this->ruleDefault;
    }

    public function setRuleDefault(RuleEffect $ruleDefault): self
    {
        $this->ruleDefault = $ruleDefault;

        return $this;
    }

    public function isOptional(): bool
    {
        return $this->optional;
    }

    public function setOptional(bool $optional): self
    {
        $this->optional = $optional;

        return $this;
    }

    public function delete(): void
    {
        Domain::events()->dispatchEntityDeleteEvent($this);
        $this->setDeleteFlag(true);
        $this->getTask()->delete();
    }
}
