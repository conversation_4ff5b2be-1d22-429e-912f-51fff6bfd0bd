<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\SessionTourRepository;
use App\Domain\Services\Domain;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: SessionTourRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class SessionTour implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\ManyToOne(inversedBy: 'sessionTours')]
    #[ORM\JoinColumn(nullable: false)]
    private Session $session;

    #[ORM\ManyToOne(inversedBy: 'sessionTours')]
    #[ORM\JoinColumn(nullable: false)]
    private Tour $tour;

    #[ORM\Column]
    private \DateTimeImmutable $start;

    // end is a reserved keyword in PostgreSQL
    #[ORM\Column(name: '"end"', nullable: true)]
    private ?\DateTimeImmutable $end = null;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getSession(): Session
    {
        return $this->session;
    }

    public function setSession(Session $session): self
    {
        $this->session = $session;

        return $this;
    }

    public function getTour(): Tour
    {
        return $this->tour;
    }

    public function setTour(Tour $tour): self
    {
        $this->tour = $tour;
        $this->tour->addSessionTour($this);

        return $this;
    }

    public function getStart(): ?\DateTimeImmutable
    {
        return $this->start;
    }

    public function setStart(\DateTimeInterface $start): self
    {
        $this->start = \DateTimeImmutable::createFromInterface($start);

        return $this;
    }

    public function getEnd(): ?\DateTimeImmutable
    {
        return $this->end;
    }

    public function setEnd(\DateTimeInterface $end): self
    {
        $this->end = \DateTimeImmutable::createFromInterface($end);

        return $this;
    }

    public function delete(): void
    {
        Domain::events()->dispatchEntityDeleteEvent($this);
    }
}
