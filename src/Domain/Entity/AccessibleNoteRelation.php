<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Annotations\StatusIn;
use App\Domain\Entity\Enum\Status\TemplateStatus;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\DeleteFlagTrait;
use App\Domain\Entity\Trait\StatusTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\AccessibleNoteRelationRepository;
use App\Domain\Services\Domain;
use App\Exception\BadRequestException;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: AccessibleNoteRelationRepository::class)]
#[StatusIn(TemplateStatus::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class AccessibleNoteRelation implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;
    /** @use StatusTrait<TemplateStatus> */
    use StatusTrait;
    use DeleteFlagTrait;

    #[ORM\ManyToOne(cascade: ['persist'], inversedBy: 'accessibleNoteRelations')]
    #[ORM\JoinColumn(nullable: false)]
    private AccessibleNote $accessibleNote;

    #[ORM\ManyToOne(inversedBy: 'accessibleNoteRelations')]
    #[ORM\JoinColumn(nullable: false)]
    private Order $order;

    #[ORM\Column]
    private int $sequenceNumber = 0;

    /**
     * @throws BadRequestException
     */
    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->setStatus(TemplateStatus::ACTIVE);
    }

    public function getAccessibleNote(): AccessibleNote
    {
        return $this->accessibleNote;
    }

    public function setAccessibleNote(AccessibleNote $accessibleNote): self
    {
        $this->accessibleNote = $accessibleNote;

        return $this;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): self
    {
        $this->order = $order;

        return $this;
    }

    public function getSequenceNumber(): int
    {
        return $this->sequenceNumber;
    }

    public function setSequenceNumber(int $sequenceNumber): self
    {
        $this->sequenceNumber = $sequenceNumber;

        return $this;
    }

    public function delete(): void
    {
        Domain::events()->dispatchEntityDeleteEvent($this);
        $this->setDeleteFlag(true);
        $this->getAccessibleNote()->delete();
    }
}
