<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Types\MaterialType;
use App\Domain\Entity\Interfaces\ElementOptionInterface;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\DefaultMaterialRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: DefaultMaterialRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class DefaultMaterial implements EntityInterface, ElementOptionInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 100)]
    private string $name;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $legalNumber = null;

    #[ORM\Column(length: 20, enumType: MaterialType::class)]
    private MaterialType $materialType;

    #[ORM\Column(length: 50, nullable: false)]
    private string $materialId;

    /**
     * @var Collection<int, DefaultMaterialRelation>
     */
    #[ORM\OneToMany(targetEntity: DefaultMaterialRelation::class, mappedBy: 'defaultMaterial', orphanRemoval: true)]
    private Collection $defaultMaterialRelations;

    #[ORM\JoinColumn(nullable: true)]
    private ?string $sourceSystemId = null;  // @phpstan-ignore property.unusedType (to be used in future)

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->defaultMaterialRelations = new ArrayCollection();
    }

    public function getMaterialId(): string
    {
        return $this->materialId;
    }

    public function setMaterialId(string $materialId): self
    {
        $this->materialId = $materialId;

        return $this;
    }

    public function getName(): string
    {
        return $this->name ?? throw new \RuntimeException('Name is not set in DefaultMaterial');
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getLegalNumber(): ?string
    {
        return $this->legalNumber;
    }

    public function setLegalNumber(?string $legalNumber): self
    {
        $this->legalNumber = $legalNumber;

        return $this;
    }

    public function getMaterialType(): ?MaterialType
    {
        return $this->materialType;
    }

    public function setMaterialType(MaterialType $materialType): self
    {
        $this->materialType = $materialType;

        return $this;
    }

    /**
     * @return Collection<int, DefaultMaterialRelation>
     */
    public function getDefaultMaterialRelations(): Collection
    {
        return $this->defaultMaterialRelations;
    }

    public function addDefaultMaterialRelation(DefaultMaterialRelation $defaultMaterialRelation): self
    {
        if (!$this->defaultMaterialRelations->contains($defaultMaterialRelation)) {
            $this->defaultMaterialRelations->add($defaultMaterialRelation);
            $defaultMaterialRelation->setDefaultMaterial($this);
        }

        return $this;
    }

    public function getSourceSystemId(): ?string
    {
        return $this->sourceSystemId;
    }
}
