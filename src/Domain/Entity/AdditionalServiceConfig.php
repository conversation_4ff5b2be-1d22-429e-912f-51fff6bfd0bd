<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Country;
use App\Domain\Entity\Enum\ElementDataSource;
use App\Domain\Entity\Enum\Types\EquipmentType;
use App\Domain\Entity\Interfaces\ElementOptionInterface;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\ElementOption;
use App\Domain\Repository\AdditionalServiceConfigRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: AdditionalServiceConfigRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class AdditionalServiceConfig implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10, enumType: Country::class)]
    private Country $country;

    #[ORM\ManyToOne]
    private ?Branch $branch = null;

    #[ORM\Column(length: 20, nullable: true, enumType: EquipmentType::class)]
    private ?EquipmentType $equipmentType = null;

    #[ORM\ManyToOne]
    private ?Equipment $equipment = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private DefaultTask $defaultTask;

    /**
     * @var Collection<int, DefaultMaterialRelation>
     */
    #[ORM\OneToMany(
        targetEntity: DefaultMaterialRelation::class,
        mappedBy: 'additionalServiceConfig',
        orphanRemoval: true
    )]
    #[ORM\OrderBy(['sequenceNumber' => 'ASC'])]
    private Collection $defaultMaterialRelations;

    /**
     * @var Collection<int, DefaultUnitRelation>
     */
    #[ORM\OneToMany(targetEntity: DefaultUnitRelation::class, mappedBy: 'additionalServiceConfig', orphanRemoval: true)]
    #[ORM\OrderBy(['sequenceNumber' => 'ASC'])]
    private Collection $defaultUnitRelations;

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->defaultMaterialRelations = new ArrayCollection();
        $this->defaultUnitRelations = new ArrayCollection();
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(Country $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getBranch(): ?Branch
    {
        return $this->branch;
    }

    public function setBranch(?Branch $branch): self
    {
        $this->branch = $branch;

        return $this;
    }

    public function getEquipmentType(): ?EquipmentType
    {
        return $this->equipmentType;
    }

    public function setEquipmentType(?EquipmentType $equipmentType): self
    {
        $this->equipmentType = $equipmentType;

        return $this;
    }

    public function getEquipment(): ?Equipment
    {
        return $this->equipment;
    }

    public function setEquipment(?Equipment $equipment): self
    {
        $this->equipment = $equipment;

        return $this;
    }

    public function getDefaultTask(): DefaultTask
    {
        return $this->defaultTask ?? throw new \RuntimeException('DefaultTask is not set');
    }

    public function setDefaultTask(DefaultTask $defaultTask): self
    {
        $this->defaultTask = $defaultTask;

        return $this;
    }

    /**
     * @return Collection<int, DefaultMaterialRelation>
     */
    public function getDefaultMaterialRelations(): Collection
    {
        return $this->defaultMaterialRelations;
    }

    /**
     * @return Collection<int, DefaultUnitRelation>
     */
    public function getDefaultUnitRelations(): Collection
    {
        return $this->defaultUnitRelations;
    }

    /**
     * @return array<ElementOptionInterface>
     */
    public function getDataSourceElementOptions(ElementDataSource $dataSource): array
    {
        if (ElementDataSource::UNIT === $dataSource) {
            return $this->getDefaultUnitRelations()->toArray();
        }

        return $this
            ->getDefaultMaterialRelations()
            ->map(
                static fn (DefaultMaterialRelation $defaultMaterialRelation): DefaultMaterial
                    => $defaultMaterialRelation->getDefaultMaterial()
            )
            ->toArray();
    }

    /**
     * @return ElementOption[]
     */
    public function generateDataSourceElementOptions(ElementDataSource $dataSource): array
    {
        $values = [];

        switch ($dataSource) {
            case ElementDataSource::UNIT:
                foreach ($this->getDefaultUnitRelations() as $defaultUnitRelation) {
                    $values[] = new ElementOption(
                        name: $defaultUnitRelation->getUnit()->value,
                        id: $defaultUnitRelation->getId(),
                    );
                }
                break;

            case ElementDataSource::MATERIAL:
                foreach ($this->getDefaultMaterialRelations() as $defaultMaterialRelation) {
                    $defaultMaterial = $defaultMaterialRelation->getDefaultMaterial();
                    $values[] = new ElementOption(
                        name: $defaultMaterial->getMaterialId(),
                        id: $defaultMaterial->getId(),
                    );
                }
                break;
        }

        return $values;
    }
}
