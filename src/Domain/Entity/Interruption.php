<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Annotations\StatusIn;
use App\Domain\Entity\Enum\Status\InterruptionStatus;
use App\Domain\Entity\Enum\Status\TaskStatus;
use App\Domain\Entity\Enum\Types\BookingType;
use App\Domain\Entity\Enum\Types\ElementType;
use App\Domain\Entity\Enum\Types\InterruptionType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasBookableStatus;
use App\Domain\Entity\Interfaces\HasTaskGroups;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\StatusTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\AdditionalInformation;
use App\Domain\Entity\ValueObject\Booking;
use App\Domain\Repository\InterruptionRepository;
use App\Domain\Services\Domain;
use App\Exception\BadRequestException;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

/**
 * @implements HasBookableStatus<InterruptionStatus>
 */
#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: InterruptionRepository::class)]
#[StatusIn(InterruptionStatus::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class Interruption implements EntityInterface, HasBookableStatus, HasTaskGroups, HasTenant
{
    use CommonTrait;
    use TenantTrait;
    /** @use StatusTrait<InterruptionStatus> */
    use StatusTrait;

    #[ORM\Column(length: 20, enumType: InterruptionType::class)]
    private InterruptionType $type;

    /**
     * @var Collection<int, TaskGroup>
     */
    #[ORM\OneToMany(targetEntity: TaskGroup::class, mappedBy: 'interruption', cascade: ['persist'])]
    #[ORM\OrderBy(['sequenceNumber' => 'ASC'])]
    private Collection $taskGroups;

    #[ORM\ManyToOne(inversedBy: 'interruptions')]
    private ?Tour $tour = null;

    #[ORM\Column(length: 255)]
    private string $clientUuid;

    #[ORM\Column(length: 36, nullable: true)]
    private ?string $accessibleInterruptionId = null;

    #[ORM\Column(length: 36, nullable: true)]
    private ?string $defaultInterruptionId = null;

    #[ORM\ManyToOne(inversedBy: 'interruptions')]
    #[ORM\JoinColumn(nullable: false)]
    private Session $session;

    #[ORM\Column(length: 50, nullable: false)]
    private string $externalId;

    #[ORM\Column(nullable: true, options: ['default' => null])]
    private ?InterruptionStatus $sentToSap = null;

    /**
     * @var AdditionalInformation[]
     */
    #[ORM\Column(type: 'json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private array $additionalInformationItems = [];

    /**
     * @var Booking[]
     */
    #[ORM\Column(type: 'json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private array $bookingItems = [];

    #[ORM\Column(type: Types::TEXT, nullable: false)]
    private string $description;

    /**
     * @throws BadRequestException
     */
    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->taskGroups = new ArrayCollection();
        $this->setStatus(InterruptionStatus::CREATED);
    }

    public function getType(): InterruptionType
    {
        return $this->type ?? throw new \RuntimeException('Type is not set');
    }

    public function setType(InterruptionType $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection<int, TaskGroup>
     */
    public function getTaskGroups(): Collection
    {
        return $this->taskGroups;
    }

    public function addTaskGroup(TaskGroup $taskGroup): self
    {
        if (!$this->taskGroups->contains($taskGroup)) {
            $this->taskGroups->add($taskGroup);
            $taskGroup->setInterruption($this);
        }

        return $this;
    }

    public function removeTaskGroup(TaskGroup $taskGroup): self
    {
        // set the owning side to null (unless already changed)
        if ($this->taskGroups->removeElement($taskGroup) && $taskGroup->getInterruption() === $this) {
            $taskGroup->setInterruption(null);
        }

        return $this;
    }

    /**
     * @return AdditionalInformation[]
     */
    public function getAdditionalInformation(): array
    {
        return $this->additionalInformationItems;
    }

    public function addAdditionalInformation(AdditionalInformation $additionalInformation): self
    {
        $this->additionalInformationItems[] = $additionalInformation;

        usort(
            $this->additionalInformationItems,
            static fn (AdditionalInformation $a, AdditionalInformation $b): int => $a->sequence <=> $b->sequence,
        );

        return $this;
    }

    public function getTour(): ?Tour
    {
        return $this->tour;
    }

    public function setTour(?Tour $tour): self
    {
        $this->tour = $tour;

        return $this;
    }

    /**
     * @return Booking[]
     */
    public function getBookings(): array
    {
        return $this->bookingItems;
    }

    public function addBooking(Booking $booking): static
    {
        $this->bookingItems[] = $booking;

        return $this;
    }

    public function removeBooking(Booking $booking): static
    {
        $this->bookingItems = array_values(array_filter(
            $this->bookingItems,
            static fn (Booking $existingBooking): bool => $existingBooking->id !== $booking->id
        ));

        return $this;
    }

    public function getLastTourUpdate(): ?\DateTimeInterface
    {
        return $this->tour?->getLastTourUpdate();
    }

    public function getClientUuid(): ?string
    {
        return $this->clientUuid;
    }

    public function setClientUuid(string $clientUuid): self
    {
        $this->clientUuid = $clientUuid;

        return $this;
    }

    public function getAccessibleInterruptionId(): ?string
    {
        return $this->accessibleInterruptionId;
    }

    public function setAccessibleInterruptionId(?string $accessibleInterruptionId): self
    {
        $this->accessibleInterruptionId = $accessibleInterruptionId;

        return $this;
    }

    public function getDefaultInterruptionId(): ?string
    {
        return $this->defaultInterruptionId;
    }

    public function setDefaultInterruptionId(?string $defaultInterruptionId): self
    {
        $this->defaultInterruptionId = $defaultInterruptionId;

        return $this;
    }

    public function getSession(): Session
    {
        return $this->session ?? throw new \RuntimeException('Session is not set');
    }

    public function setSession(Session $session): self
    {
        $this->session = $session;

        return $this;
    }

    public function getStart(): ?\DateTimeImmutable
    {
        $bookings = array_values(array_filter(
            $this->bookingItems,
            static fn (Booking $booking): bool => BookingType::START === $booking->type
        ));

        return count($bookings) > 0 ? $bookings[0]->timestamp : null;
    }

    public function getEnd(): ?\DateTimeImmutable
    {
        $bookings = array_values(array_filter(
            $this->bookingItems,
            static fn (Booking $booking): bool => BookingType::END === $booking->type
        ));

        return count($bookings) > 0 ? $bookings[0]->timestamp : null;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getSentToSap(): ?InterruptionStatus
    {
        return $this->sentToSap;
    }

    public function setSentToSap(InterruptionStatus $sentToSap): self
    {
        $this->sentToSap = $sentToSap;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return ValueObject\Element[]
     */
    public function getCompletedPhotoElements(): array
    {
        $photoElements = [];

        foreach ($this->getTaskGroups() as $taskGroup) {
            foreach ($taskGroup->getTaskRelations() as $taskRelation) {
                $task = $taskRelation->getTask();

                foreach ($task->getElements() as $element) {
                    if (ElementType::PHOTO === $element->type && TaskStatus::COMPLETED === $task->getStatus()) {
                        $photoElements[] = $element;
                    }
                }
            }
        }

        return $photoElements;
    }

    public function getFirstCompletedSignatureTask(): ?Task
    {
        foreach ($this->getTaskGroups() as $taskGroup) {
            foreach ($taskGroup->getTaskRelations() as $taskRelation) {
                $task = $taskRelation->getTask();

                if ('signature' === $task->getType() && TaskStatus::COMPLETED === $task->getStatus()) {
                    return $task;
                }
            }
        }

        return null;
    }

    public function complete(): void
    {
        Domain::workflow()->applyForInterruption($this, 'complete');
    }

    public function delete(): void
    {
        Domain::events()->dispatchEntityDeleteEvent($this);

        foreach ($this->getTaskGroups() as $taskGroup) {
            $taskGroup->delete();
        }
    }

    public function getBookingLabel(): string
    {
        return $this->getType()->value;
    }
}
