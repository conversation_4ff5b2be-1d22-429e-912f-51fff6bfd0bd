<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\BookingSource;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Entity\ValueObject\Booking;
use App\Domain\Repository\SessionBookingRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: SessionBookingRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['sessionId', 'tenant'])]
#[ORM\Index(fields: ['tenant'])]
class SessionBooking implements HasTenant, EntityInterface
{
    use TenantTrait;
    use CommonTrait;

    public function __construct(
        #[ORM\Column(length: 40, nullable: false)]
        private readonly string $sessionId,

        #[ORM\Column(length: 20, nullable: false)]
        private readonly BookingSource $source,

        #[ORM\Column(length: 40, nullable: false)]
        private readonly string $sourceId,

        #[ORM\Column(type: 'json_document', nullable: false)]
        private readonly Booking $booking,

        #[ORM\Column(length: 250, nullable: false)]
        private readonly string $description,
    ) {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getSource(): BookingSource
    {
        return $this->source;
    }

    public function getSourceId(): string
    {
        return $this->sourceId;
    }

    public function getBooking(): Booking
    {
        return $this->booking;
    }

    public function getDescription(): string
    {
        return $this->description;
    }
}
