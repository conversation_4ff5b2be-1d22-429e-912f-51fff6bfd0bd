<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Types\MobileAppType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\ValueObject\ReleaseFile;
use App\Domain\Repository\MobileAppReleaseRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: MobileAppReleaseRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\UniqueConstraint(fields: ['version'])]
class MobileAppRelease implements EntityInterface
{
    use CommonTrait;

    public function __construct(
        #[ORM\Column(length: 20, nullable: false)]
        private readonly string $version,

        #[ORM\Column(type: Types::TEXT, length: 25000, nullable: false)]
        private readonly string $releaseNotes,

        #[ORM\Column(name: '"type"', length: 20, nullable: false, enumType: MobileAppType::class)]
        private readonly MobileAppType $type,

        /**
         * @var ReleaseFile[]
         */
        #[ORM\Column(type: 'json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
        private array $files = [],
    ) {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
    }

    public function getVersion(): string
    {
        return $this->version;
    }

    public function getReleaseNotes(): string
    {
        return $this->releaseNotes;
    }

    /**
     * @return ReleaseFile[]
     */
    public function getFiles(): array
    {
        return $this->files;
    }

    public function getType(): MobileAppType
    {
        return $this->type;
    }
}
