<?php

declare(strict_types=1);

namespace App\Domain\Entity\Interfaces;

use App\Domain\Entity\Enum\Types\TaskGroupType;
use App\Domain\Entity\TaskGroup;
use Doctrine\Common\Collections\Collection;

/**
 * @template T of TaskRelationTemplate
 */
interface TaskGroupTemplate
{
    public function getId(): string;

    /**
     * @return Collection<int, T>
     */
    public function getTaskTemplateRelations(): Collection;

    public function getSequenceNumber(): int;

    public function getTitle(): string;

    public function getType(): ?TaskGroupType;

    public function createBasicTaskGroup(): TaskGroup;
}
