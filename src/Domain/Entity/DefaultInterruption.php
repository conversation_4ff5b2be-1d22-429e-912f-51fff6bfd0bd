<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Types\InterruptionType;
use App\Domain\Entity\Interfaces\EntityInterface;
use App\Domain\Entity\Interfaces\HasTaskGroupTemplates;
use App\Domain\Entity\Interfaces\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\DefaultInterruptionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

/**
 * @implements HasTaskGroupTemplates<DefaultTaskGroup>
 */
#[ORM\Table(schema: 'public')]
#[ORM\Entity(repositoryClass: DefaultInterruptionRepository::class)]
#[ORM\Index(fields: ['modifiedAt'])] // Used by hermes-data sync
#[ORM\Index(fields: ['tenant'])]
class DefaultInterruption implements EntityInterface, HasTaskGroupTemplates, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(type: Types::TEXT, nullable: false)]
    private string $description;

    #[ORM\Column(length: 20, nullable: false, enumType: InterruptionType::class)]
    private InterruptionType $type;

    /**
     * @var Collection<int, DefaultInterruptionRelation>
     */
    #[ORM\OneToMany(
        targetEntity: DefaultInterruptionRelation::class,
        mappedBy: 'defaultInterruption',
        orphanRemoval: true
    )]
    private Collection $defaultInterruptionRelations;

    /**
     * @var Collection<int, DefaultTaskGroup>
     */
    #[ORM\OneToMany(targetEntity: DefaultTaskGroup::class, mappedBy: 'defaultInterruption')]
    private Collection $defaultTaskGroups;

    #[ORM\Column(length: 50, nullable: false)]
    private string $externalId;

    /**
     * @var ValueObject\AdditionalInformation[]
     */
    #[ORM\Column(type: 'json_document', nullable: false, options: ['jsonb' => true, 'default' => '[]'])]
    private array $additionalInformationItems = [];

    public function __construct()
    {
        $this->id = Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->modifiedAt = new \DateTimeImmutable();
        $this->defaultInterruptionRelations = new ArrayCollection();
        $this->defaultTaskGroups = new ArrayCollection();
    }

    public function getDescription(): string
    {
        return $this->description ?? throw new \RuntimeException('Description is not set');
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getType(): InterruptionType
    {
        return $this->type ?? throw new \RuntimeException('Type is not set');
    }

    public function setType(InterruptionType $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection<int, DefaultInterruptionRelation>
     */
    public function getDefaultInterruptionRelations(): Collection
    {
        return $this->defaultInterruptionRelations;
    }

    public function addDefaultInterruptionRelation(DefaultInterruptionRelation $defaultInterruptionRelation): self
    {
        if (!$this->defaultInterruptionRelations->contains($defaultInterruptionRelation)) {
            $this->defaultInterruptionRelations->add($defaultInterruptionRelation);
            $defaultInterruptionRelation->setDefaultInterruption($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, DefaultTaskGroup>
     */
    public function getDefaultTaskGroups(): Collection
    {
        return $this->defaultTaskGroups;
    }

    public function addDefaultTaskGroup(DefaultTaskGroup $defaultTaskGroup): self
    {
        if (!$this->defaultTaskGroups->contains($defaultTaskGroup)) {
            $this->defaultTaskGroups->add($defaultTaskGroup);
            $defaultTaskGroup->setDefaultInterruption($this);
        }

        return $this;
    }

    public function removeDefaultTaskGroup(DefaultTaskGroup $defaultTaskGroup): self
    {
        // set the owning side to null (unless already changed)
        if (
            $this->defaultTaskGroups->removeElement($defaultTaskGroup)
            && $defaultTaskGroup->getDefaultInterruption() === $this
        ) {
            $defaultTaskGroup->setDefaultInterruption(null);
        }

        return $this;
    }

    /**
     * @return ValueObject\AdditionalInformation[]
     */
    public function getAdditionalInformation(): array
    {
        return $this->additionalInformationItems;
    }

    public function getTaskGroupTemplates(): Collection
    {
        return $this->getDefaultTaskGroups();
    }

    public function createInterruption(): Interruption
    {
        $interruption = new Interruption()
            ->setType($this->getType())
            ->setExternalId($this->getExternalId())
            ->setDefaultInterruptionId($this->getId())
            ->setDescription($this->getDescription())
        ;

        foreach ($this->getAdditionalInformation() as $additionalInformation) {
            $interruption->addAdditionalInformation($additionalInformation);
        }

        return $interruption;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }
}
