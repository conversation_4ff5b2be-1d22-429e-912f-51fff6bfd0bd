<?php

declare(strict_types=1);

namespace App\Domain\Util;

trait With
{
    /**
     * This is private because of the caveat, that property renaming in the class that uses this trait class does not
     * refactor the usages of this method. So to limit the extent of that limitation, if we use this method only inside
     * the class itself, refactoring of the property will require changes only in the class itself, not in the users of
     * the class.
     *
     * @param array<string, mixed> $properties
     */
    private function with(...$properties): self
    {
        $currentProperties = (array) $this;

        foreach (array_keys($properties) as $name) {
            if (!array_key_exists($name, $currentProperties)) {
                throw new \InvalidArgumentException('Property '.$name.' does not exist in '.self::class);
            }
        }

        $properties += $currentProperties;

        // @phpstan-ignore-next-line
        return new self(...$properties);
    }

    /**
     * @param array<string> $properties
     */
    private function without(array $properties): self
    {
        $currentProperties = (array) $this;

        foreach ($properties as $name) {
            if (!array_key_exists($name, $currentProperties)) {
                throw new \InvalidArgumentException('Property '.$name.' does not exist in '.self::class);
            }

            unset($currentProperties[$name]);
        }

        // @phpstan-ignore-next-line
        return new self(...$currentProperties);
    }
}
