<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Enum\Status\TourStatus;
use App\Domain\Entity\Equipment;
use App\Domain\Entity\Tour;
use App\Domain\Entity\Tracking;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;

/**
 * @extends ServiceEntityRepository<Tracking>
 */
class TrackingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Tracking::class);
    }

    public function findOrCreate(
        string $sessionId,
        string $deviceId,
        ?Equipment $equipment,
        ?string $tourExternalId,
        \DateTimeImmutable $date,
    ): Tracking {
        $tracking = $this->findOneBy([
            'date' => $date,
            'tourExternalId' => $tourExternalId,
            'sessionId' => $sessionId,
            'equipmentExternalId' => $equipment?->getExternalId(),
        ]);

        if (null === $tracking) {
            $tracking = new Tracking(
                sessionId: $sessionId,
                deviceId: $deviceId,
                date: $date,
                equipmentExternalId: $equipment?->getExternalId(),
                tourExternalId: $tourExternalId,
                equipmentLicensePlate: $equipment?->getLicensePlate(),
            );

            $this->getEntityManager()->persist($tracking);
        }

        return $tracking;
    }

    /**
     * @param array<string> $accessibleBranchIds
     *
     * @return array<array{tracking: Tracking, tourId: string, licensePlate:string}>
     *
     * @throws \DateMalformedStringException
     */
    public function findTourTrackingsBySearchCriteria(
        UserSearchCriteria $userSearchCriteria,
        array $accessibleBranchIds,
    ): array {
        $minDate = $userSearchCriteria->filters['min_date']->value ?? null;
        $maxDate = $userSearchCriteria->filters['max_date']->value ?? null;
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;
        $limit = $userSearchCriteria->perPage;

        $qb = $this->createQueryBuilder('tr')
            ->select('tr as tracking', 'eq.licensePlate', 'tour.id as tourId')
            ->leftJoin(Equipment::class, 'eq', Join::WITH, 'tr.equipmentExternalId = eq.externalId')
            ->leftJoin(Tour::class, 'tour', Join::WITH, 'tr.tourExternalId = tour.externalId')

            ->andWhere('tr.tourExternalId IS NOT NULL')
            ->andWhere('tour.status != :tourObsoleteStatus')
            ->andWhere('tour.branch IN (:accessibleBranchIds)')

            ->setParameter('tourObsoleteStatus', TourStatus::OBSOLETE->value)
            ->setParameter('accessibleBranchIds', $accessibleBranchIds)

            ->orderBy('tr.date', 'DESC')

            ->setFirstResult($offset)
            ->setMaxResults($limit)
        ;

        if (null !== $minDate && is_string($minDate)) {
            $minDate = new \DateTimeImmutable($minDate);
            $qb->andWhere('tr.date >= :minDate')
                ->setParameter('minDate', $minDate->setTime(0, 0));
        }

        if (null !== $maxDate && is_string($maxDate)) {
            $maxDate = new \DateTimeImmutable($maxDate);
            $qb->andWhere('tr.date <= :maxDate')
                ->setParameter('maxDate', $maxDate->setTime(23, 59));
        }

        if (is_string($search)) {
            $qb->andWhere('ILIKE(tr.search, :search) = TRUE')
               ->setParameter('search', '%'.$search.'%');
        }

        /** @phpstan-var array<array{tracking: Tracking, tourId: string, licensePlate:string}> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @param array<string> $accessibleBranchIds
     *
     * @throws \DateMalformedStringException
     */
    public function findTourTrackingsCountBySearchCriteria(
        UserSearchCriteria $userSearchCriteria,
        array $accessibleBranchIds,
    ): int {
        $minDate = $userSearchCriteria->filters['min_date']->value ?? null;
        $maxDate = $userSearchCriteria->filters['max_date']->value ?? null;
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $qb = $this->createQueryBuilder('tr')
            ->select('COUNT(tr.id)')
            ->leftJoin(Equipment::class, 'eq', Join::WITH, 'tr.equipmentExternalId = eq.externalId')
            ->leftJoin(Tour::class, 'tour', Join::WITH, 'tr.tourExternalId = tour.externalId')

            ->andWhere('tr.tourExternalId IS NOT NULL')
            ->andWhere('tour.status != :tourObsoleteStatus')
            ->andWhere('tour.branch IN (:accessibleBranchIds)')

            ->setParameter('tourObsoleteStatus', TourStatus::OBSOLETE->value)
            ->setParameter('accessibleBranchIds', $accessibleBranchIds)
        ;

        if (null !== $minDate && is_string($minDate)) {
            $minDate = new \DateTimeImmutable($minDate);
            $qb->andWhere('tr.date >= :minDate')
                ->setParameter('minDate', $minDate->setTime(0, 0));
        }

        if (null !== $maxDate && is_string($maxDate)) {
            $maxDate = new \DateTimeImmutable($maxDate);
            $qb->andWhere('tr.date <= :maxDate')
                ->setParameter('maxDate', $maxDate->setTime(23, 59));
        }

        if (is_string($search)) {
            $qb->andWhere('ILIKE(tr.search, :search) = TRUE')
               ->setParameter('search', '%'.$search.'%');
        }

        /** @var scalar $result */
        $result = $qb->getQuery()->getSingleScalarResult();

        return (int) $result;
    }
}
