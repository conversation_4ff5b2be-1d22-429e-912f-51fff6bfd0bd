<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\Task;
use Symfony\Contracts\EventDispatcher\Event;

class TaskStatusChangedEvent extends Event
{
    final public const string NAME = DomainEvent::TASK_STATUS_CHANGED->value;

    public function __construct(
        private readonly Task $task,
        private readonly \DateTimeImmutable $timestamp,
        private readonly ?float $latitude,
        private readonly ?float $longitude,
        private readonly ?float $mileage,
    ) {
    }

    public function getTask(): Task
    {
        return $this->task;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getMileage(): ?float
    {
        return $this->mileage;
    }
}
