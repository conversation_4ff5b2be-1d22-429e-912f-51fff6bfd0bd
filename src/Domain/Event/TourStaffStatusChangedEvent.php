<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\TourStaff;
use Symfony\Contracts\EventDispatcher\Event;

class TourStaffStatusChangedEvent extends Event
{
    final public const string NAME = DomainEvent::TOUR_STAFF_STATUS_CHANGED->value;

    public function __construct(
        private readonly TourStaff $tourStaff,
        private readonly \DateTimeImmutable $timestamp,
        private readonly ?float $latitude,
        private readonly ?float $longitude,
        private readonly ?float $mileage,
    ) {
    }

    public function getTourStaff(): TourStaff
    {
        return $this->tourStaff;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getMileage(): ?float
    {
        return $this->mileage;
    }
}
