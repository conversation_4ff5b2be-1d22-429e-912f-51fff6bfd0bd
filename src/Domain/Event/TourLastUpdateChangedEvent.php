<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\Tour;
use Symfony\Contracts\EventDispatcher\Event;

class TourLastUpdateChangedEvent extends Event
{
    final public const string NAME = DomainEvent::TOUR_LAST_UPDATE_CHANGED->value;

    public function __construct(
        private readonly Tour $tour,
        private readonly ?\DateTimeImmutable $timestamp = null,
    ) {
    }

    public function getTour(): Tour
    {
        return $this->tour;
    }

    public function getTimestamp(): ?\DateTimeImmutable
    {
        return $this->timestamp;
    }
}
