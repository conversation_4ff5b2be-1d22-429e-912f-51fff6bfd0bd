<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\Enum\Status\TourStatus;
use App\Domain\Entity\Tour;
use Symfony\Contracts\EventDispatcher\Event;

class TourBeforeStatusChangeEvent extends Event
{
    final public const string NAME = DomainEvent::TOUR_BEFORE_STATUS_CHANGED->value;

    public function __construct(
        private readonly Tour $tour,
        private readonly TourStatus $nextStatus,
        private readonly \DateTimeImmutable $timestamp,
        private readonly ?float $latitude,
        private readonly ?float $longitude,
        private readonly ?float $mileage,
    ) {
    }

    public function getTour(): Tour
    {
        return $this->tour;
    }

    public function getNextStatus(): TourStatus
    {
        return $this->nextStatus;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getMileage(): ?float
    {
        return $this->mileage;
    }
}
