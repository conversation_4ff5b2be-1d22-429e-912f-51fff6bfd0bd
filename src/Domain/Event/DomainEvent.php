<?php

declare(strict_types=1);

namespace App\Domain\Event;

enum DomainEvent: string
{
    // Tours
    case TOUR_STATUS_CHANGED = 'domain.tour.status.changed';
    case TOUR_BEFORE_STATUS_CHANGED = 'domain.tour.before.status.changed';
    case TOUR_LAST_UPDATE_CHANGED = 'domain.tour.last-update.changed';

    case TOUR_EQUIPMENT_STATUS_CHANGED = 'domain.tour_equipment.status.changed';
    case TOUR_STAFF_STATUS_CHANGED = 'domain.tour_staff.status.changed';
    case INTERRUPTION_STATUS_CHANGED = 'domain.interruption.status.changed';
    case STAFF_STATUS_CHANGED = 'domain.staff.status.changed';
    case EQUIPMENT_STATUS_CHANGED = 'domain.equipment.status.changed';
    case TASK_GROUP_STATUS_CHANGED = 'domain.task_group.status.changed';
    case TASK_STATUS_CHANGED = 'domain.task.status.changed';

    // Users
    case DRIVER_CREATED = 'domain.driver.created';
    case DRIVER_UPDATED = 'domain.driver.updated';
    case USER_LOGOUT = 'domain.user.logout';

    // Order
    case ORDER_STATUS_CHANGED = 'domain.order.status.changed';
    case ORDER_DOCUMENT_PDF_CREATED = 'domain.order.document.pdf.created';

    case ORDER_INFO_FILE_DOWNLOAD = 'domain.order.info_file.download';
    case ORDER_FOCUS = 'domain.order.focus';
    case ORDER_UNFOCUS = 'domain.order.unfocus';

    // Device messages
    case DEVICE_MESSAGE_THREAD_UPDATED = 'domain.device_message.thread_updated';
    case DEVICE_MESSAGE_THREAD_MARKED_AS_READ = 'domain.device_message.thread_marked_as_read';

    // Entities
    case ENTITY_DELETE = 'entity.delete';

    // Staff
    case STAFF_FOCUS = 'domain.staff.focus';

    // Device
    case DEVICE_DEBUG_MODE_CHANGE = 'domain.device.debug-mode';
}
