<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\User;
use Symfony\Contracts\EventDispatcher\Event;

class UserSessionEndEvent extends Event
{
    final public const string NAME = DomainEvent::USER_LOGOUT->value;

    public function __construct(
        private readonly User $user,
        private readonly \DateTimeImmutable $timestamp,
        private readonly ?float $latitude,
        private readonly ?float $longitude,
        private readonly ?float $mileage,
    ) {
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getMileage(): ?float
    {
        return $this->mileage;
    }
}
