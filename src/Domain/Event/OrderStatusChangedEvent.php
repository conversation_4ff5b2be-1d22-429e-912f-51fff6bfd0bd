<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\Order;
use Symfony\Contracts\EventDispatcher\Event;

class OrderStatusChangedEvent extends Event
{
    final public const string NAME = DomainEvent::ORDER_STATUS_CHANGED->value;

    public function __construct(
        private readonly Order $order,
        private readonly \DateTimeImmutable $timestamp,
        private readonly ?float $latitude,
        private readonly ?float $longitude,
        private readonly ?float $mileage,
        private readonly ?string $previousState,
    ) {
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getMileage(): ?float
    {
        return $this->mileage;
    }

    public function getPreviousState(): ?string
    {
        return $this->previousState;
    }
}
