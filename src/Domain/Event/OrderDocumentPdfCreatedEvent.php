<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\Document;
use Symfony\Contracts\EventDispatcher\Event;

class OrderDocumentPdfCreatedEvent extends Event
{
    final public const string NAME = DomainEvent::ORDER_DOCUMENT_PDF_CREATED->value;

    public function __construct(
        private readonly Document $orderDocument,
    ) {
    }

    public function getOrderDocument(): Document
    {
        return $this->orderDocument;
    }
}
