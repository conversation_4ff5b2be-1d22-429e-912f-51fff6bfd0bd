<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\TourEquipment;
use Symfony\Contracts\EventDispatcher\Event;

class TourEquipmentStatusChangedEvent extends Event
{
    final public const string NAME = DomainEvent::TOUR_EQUIPMENT_STATUS_CHANGED->value;

    public function __construct(
        private readonly TourEquipment $tourEquipment,
        private readonly \DateTimeImmutable $timestamp,
        private readonly ?float $latitude,
        private readonly ?float $longitude,
        private readonly ?float $mileage,
    ) {
    }

    public function getTourEquipment(): TourEquipment
    {
        return $this->tourEquipment;
    }

    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getMileage(): ?float
    {
        return $this->mileage;
    }
}
