#!/usr/bin/env bash

# Fail on first error
set -eu

if [[ "$APP_ENV" != *"dev" ]]; then
    echo "execute tests only allowed on *dev"
    exit;
fi

# Get current script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
INPUT_PARAMS="$*"

# Generate openapi definitions for the tests
echo "Generating Hermes APP API openapi definitions for the tests..."
STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console prezero:api:dump-documentation --format hermes_app "$PROJECT_DIR"/tests/Support/Data/openapi.json

echo "Generating Portal API openapi definitions for the tests..."
STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console prezero:api:dump-documentation --format portal "$PROJECT_DIR"/tests/Support/Data/portal-openapi.json

# If the input parameters do not contain '--no-reset-db', then reset the database
if [[ "$INPUT_PARAMS" != *--no-reset-db* ]]; then
    echo "Resetting the database..."
    STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console app:database:load-dump fixtures/dump.sql
fi

# If the input parameters do not contain '--no-reset-wiremock', then reset the wiremock
if [[ "$INPUT_PARAMS" != *--no-reset-wiremock* ]]; then
    echo "Resetting the wiremock..."
    STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console app:wiremock:reset
    STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console app:dako:create-wiremock-mappings
fi

echo "Resetting the geotab-block..."
STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console app:geotab:unblock

# Remove 'no-reset-db' from the input parameters
INPUT_PARAMS="${INPUT_PARAMS//--no-reset-db/}"
INPUT_PARAMS="${INPUT_PARAMS//--no-reset-wiremock/}"

# Run the tests
rm -rf "$PROJECT_DIR"/tests/_output/*
echo "Running the tests..."
STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/vendor/bin/codecept run $INPUT_PARAMS
