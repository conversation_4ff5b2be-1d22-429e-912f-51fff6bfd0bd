<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\Api\SapGermanyTourFixtures;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

class PhonebookCest
{
    private const string BRANCH_ID = 'c42db2fb-01e0-40e6-b7ea-dc1203bd33c8';
    private const string BRANCH_ID2 = '4f892b7f-3870-4e84-8ba2-e663f3b0f60b';
    private string $branchId3 = '';
    private string $phonebookId = '';
    private string $contactPhonebookId = '';
    private string $contactId = '';
    private string $staffExtId = '';
    private string $deviceId = '';
    private string $branchExtId = '';

    public function canCreateNewBranchForPhonebook(ApiTester $I): void
    {
        $this->staffExtId = $I::TEST_STAFF_EXT_ID_PREFIX.uniqid();
        $tour1ExtId = 'random-tour-1-id-'.uniqid();
        $order1ExtId = 'random-order-1-id-'.uniqid();
        $tour1Name = 'random-tour-1-name-'.uniqid();
        $this->deviceId = 'random-device-'.uniqid();
        $this->branchExtId = 'random-branch-'.uniqid();
        $equipmentExtId = 'random-equipment-'.uniqid();
        $equipmentLicensePlate = 'random-license-plate-'.uniqid();

        $I->callSapGermanyTourUpsert(SapGermanyTourFixtures::tourRequestCreate(
            tourExtId: $tour1ExtId,
            orderExtId: $order1ExtId,
            tourName: $tour1Name,
            equipmentExtId: $equipmentExtId,
            equipmentLicensePlate: $equipmentLicensePlate,
            staffExternalId: $this->staffExtId,
            branchExternalId: $this->branchExtId,
        ));

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $I->callApiPortalBranchListAccessible();

        $branches = $I->grabDataFromResponseByJsonPath('$.items')[0];
        foreach ($branches as $branch) {
            if ($this->branchExtId === $branch['external_id']) {
                $this->branchId3 = $branch['branch_id'];
            }
        }
        $I->assertNotEmpty($this->branchId3);
    }

    public function canPostPortalPhonebook(ApiTester $I): void
    {
        $name = md5((string) rand(0, 25));
        $description = md5((string) rand(0, 100));
        $branches = [self::BRANCH_ID, self::BRANCH_ID2, $this->branchId3];

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getPhonebookRequestJson($name, $description, $branches);

        $I->callApiPortalPhonebookCreate($data);
        $I->waitUntil(function (ApiTester $I) use ($name, $description, $branches): void {
            $phonebook = $I->grabDataFromResponseByJsonPath('$')[0];
            assert(is_array($phonebook));
            assert(is_array($phonebook['branches']));
            $this->phonebookId = $phonebook['id'];
            $I->assertContains($name, $phonebook);
            $I->assertContains($description, $phonebook);
            $I->assertEquals($branches, $phonebook['branches']);
        });
    }

    #[Depends('canPostPortalPhonebook')]
    public function canPatchPortalPhonebook(ApiTester $I): void
    {
        $name = md5((string) rand(0, 25));
        $description = md5((string) rand(0, 100));
        $branches = [self::BRANCH_ID];

        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $data = $this->getPhonebookRequestJson($name, $description, $branches);
        $I->callApiPortalPhonebookPatch($this->phonebookId, $data);
        $I->waitUntil(function (ApiTester $I) use ($name, $description, $branches): void {
            $phonebook = $I->grabDataFromResponseByJsonPath('$')[0];
            assert(is_array($phonebook));
            assert(is_array($phonebook['branches']));
            $I->assertContains($name, $phonebook);
            $I->assertContains($description, $phonebook);
            $I->assertEquals($branches, $phonebook['branches']);
            $I->assertContains(self::BRANCH_ID, $phonebook['branches']);
        });

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getPhonebookRequestJson($name, $description, $branches);
        $I->callApiPortalPhonebookPatch($this->phonebookId, $data);
        $I->waitUntil(function (ApiTester $I) use ($name, $description, $branches): void {
            $phonebook = $I->grabDataFromResponseByJsonPath('$')[0];
            assert(is_array($phonebook));
            assert(is_array($phonebook['branches']));
            $I->assertContains($name, $phonebook);
            $I->assertContains($description, $phonebook);
            $I->assertEquals($branches, $phonebook['branches']);
            $I->assertContains(self::BRANCH_ID, $phonebook['branches']);
            $I->assertNotContains(self::BRANCH_ID2, $phonebook['branches']);
        });
    }

    #[Depends('canPatchPortalPhonebook')]
    public function canDeletePortalPhonebook(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-user-de', 'portal-user-de', 'portal-password');
        $I->callApiPortalPhonebookDelete($this->phonebookId);
    }

    #[Depends('canDeletePortalPhonebook')]
    public function canPostPortalPhonebookContact(ApiTester $I): void
    {
        $name = md5((string) rand(0, 25));
        $description = md5((string) rand(0, 100));
        $branches = [self::BRANCH_ID, self::BRANCH_ID2, $this->branchId3];

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getPhonebookRequestJson($name, $description, $branches);

        $I->callApiPortalPhonebookCreate($data);
        $I->waitUntil(function (ApiTester $I): void {
            $phonebook = $I->grabDataFromResponseByJsonPath('$')[0];
            assert(is_array($phonebook));
            $this->contactPhonebookId = $phonebook['id'];
        });
        $I->callApiPortalPhonebookContactCreate(
            $this->contactPhonebookId,
            $this->getContactRequestJson(
                firstName: 'testFN 1',
                lastName: 'testLN 1',
                description: 'Testcontact 1',
            ));

        $contact = $I->grabDataFromResponseByJsonPath('$')[0];
        assert(is_array($contact));
        $this->contactId = $contact['id'];

        $I->callApiPortalPhonebookContactCreate(
            $this->contactPhonebookId,
            $this->getContactRequestJson(
                firstName: 'testFN 2',
                lastName: 'testLN 2',
                description: 'Testcontact 2',
            ));

        $I->callApiPortalPhonebookContactCreate(
            $this->contactPhonebookId,
            $this->getContactRequestJson(
                firstName: 'testFN 3',
                lastName: 'testLN 3',
                description: 'Testcontact 3',
            ));
    }

    #[Depends('canPostPortalPhonebookContact')]
    public function canPatchPortalPhonebookContact(ApiTester $I): void
    {
        $updatedFN = 'testFN 2';
        $updatedLN = 'testLN 2';
        $updatedDescription = 'Testcontact 2';

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->callApiPortalPhonebookContactPatch(
            $this->contactPhonebookId,
            $this->getContactRequestJson(
                firstName: $updatedFN,
                lastName: $updatedLN,
                description: $updatedDescription,
                id: $this->contactId,
            ));
        $I->waitUntil(function (ApiTester $I) use ($updatedFN, $updatedLN, $updatedDescription): void {
            $contact = $I->grabDataFromResponseByJsonPath('$')[0];
            assert(is_array($contact));
            $I->assertEquals($updatedFN, $contact['first_name'], 'First name was not updated');
            $I->assertEquals($updatedLN, $contact['last_name'], 'Last name ws not updated');
            $I->assertEquals($updatedDescription, $contact['description'], 'Description was not updated');
        });
    }

    #[Depends('canPatchPortalPhonebookContact')]
    public function canCreateSecondPhonebookForBranch(ApiTester $I): void
    {
        $name = md5((string) rand(0, 25));
        $description = md5((string) rand(0, 100));
        $branches = [$this->branchId3];

        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');
        $data = $this->getPhonebookRequestJson($name, $description, $branches);

        $I->callApiPortalPhonebookCreate($data);
        $phonebook = $I->grabDataFromResponseByJsonPath('$')[0];
        assert(is_array($phonebook));
        $phonebookId = (string) $phonebook['id'];
        $I->assertNotEmpty($phonebookId);

        $I->callApiPortalPhonebookContactCreate(
            $phonebookId,
            $this->getContactRequestJson(
                firstName: 'aaa-testFN 2',
                lastName: 'aaaa-testLN 2',
                description: 'Testcontact 2',
            ));
        $I->callApiPortalPhonebookContactCreate(
            $phonebookId,
            $this->getContactRequestJson(
                firstName: 'zzz-testFN 3',
                lastName: 'zzz-testLN 3',
                description: 'Testcontact 3',
            ));
    }

    #[Depends('canCreateSecondPhonebookForBranch')]
    public function canSeePhonebookContactInApp(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, 'pz');
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiPhonebookContactList(debug: false);

        $contacts = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertCount(5, $contacts);
        $I->assertStringStartsWith('aaa', $contacts[0]['last_name']);
        $I->assertStringStartsWith('zzz', $contacts[4]['last_name']);
    }

    #[Depends('canSeePhonebookContactInApp')]
    public function canDeletePortalPhonebookContact(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser('portal-admin-de', 'portal-admin-de', 'portal-password');

        $I->callApiPortalPhonebookContactDelete(
            $this->contactPhonebookId,
            $this->contactId,
        );
    }

    #[Depends('canDeletePortalPhonebookContact')]
    public function canSeeChangedPhonebookContactInApp(ApiTester $I): void
    {
        $I->amAuthenticatedAsGeneratedUserAndVerify($this->staffExtId, 'pz');
        $I->amUsingDeviceWithId($this->deviceId);
        $I->callApiUserStartSessionV2();
        $I->callApiPhonebookContactList(debug: false);

        $contacts = $I->grabDataFromResponseByJsonPath('$.items')[0];
        $I->assertCount(4, $contacts);
        $I->assertStringStartsWith('aaa', $contacts[0]['last_name']);
        $I->assertStringStartsWith('zzz', $contacts[3]['last_name']);
    }

    private function getPhonebookRequestJson(string $name, string $description, array $branches): string
    {
        $data = [
            'name' => $name,
            'description' => $description,
            'branches' => $branches,
        ];

        return json_encode($data, JSON_THROW_ON_ERROR);
    }

    private function getContactRequestJson(string $firstName, string $lastName, string $description, string $countryPrefix = 'de', int $phoneNumber = 1234, bool $emergencyContact = false, ?string $id = null): string
    {
        $data = [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'country_prefix' => $countryPrefix,
            'phone_number' => $phoneNumber,
            'emergency_contact' => $emergencyContact,
            'description' => $description,
        ];
        if (null !== $id) {
            $data['id'] = $id;
        }

        return json_encode($data, JSON_THROW_ON_ERROR);
    }
}
